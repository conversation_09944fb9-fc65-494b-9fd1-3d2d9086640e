# EcoPlug Notification Icon Testing Guide

## 🎯 **Testing Objective**
Verify that EcoPlug notification icons render correctly across multiple Android versions and scenarios, ensuring:
- **Small icons** are monochrome, white-tinted in status bar
- **Large icons** show full-color EcoPlug logo in expanded/shade view
- **FCM data messages** have identical branding
- **Dark/light mode** compatibility
- **Lock screen** visibility

## 📱 **Target Testing Environments**

### **Required Android API Levels:**
- ✅ **API 24 (Android 7.0 Nougat)** - Notification channel introduction
- ✅ **API 29 (Android 10 Q)** - Notification behavior changes
- ✅ **API 33 (Android 13 Tiramisu)** - Latest notification permissions

### **Testing Scenarios:**
- 📱 Local charging notifications
- 🔔 FCM push notifications
- 🌙 Dark mode behavior
- ☀️ Light mode behavior
- 🔒 Lock screen notifications
- 📊 Multiple notification types

## 🧪 **Test Execution Steps**

### **Step 1: Setup Test Environment**

1. **Ensure your Android device/emulator is connected:**
   ```bash
   flutter devices
   ```

2. **Build and install the EcoPlug app:**
   ```bash
   flutter build apk --debug
   flutter install
   ```

3. **Run the app and navigate to test functionality or use the testing script**

### **Step 2: Run Automated Icon Tests**

#### **Option A: Using the Test Script**
```dart
// Add this to your main app or test file
import 'test_notification_icons.dart';

// Run all notification icon tests
final tester = NotificationIconTester();
await tester.runAllIconTests();

// Clean up when done
await tester.cleanupTestNotifications();
```

#### **Option B: Manual Testing via App UI**
1. Navigate to any screen with notification functionality
2. Use the FloatingActionButton or menu to trigger notifications
3. Follow the manual verification steps below

### **Step 3: Local Charging Notification Test**

1. **Trigger charging notification:**
   ```dart
   // Use the existing charging service
   final chargingService = ActiveChargingNotificationService();
   await chargingService.testChargingNotification();
   ```

2. **Verify in Android notification tray:**
   - ✅ Status bar shows monochrome white-tinted EcoPlug icon
   - ✅ Pull down notification shade
   - ✅ Expanded view shows full-color EcoPlug logo
   - ✅ Notification shows "Charging Active • XX%" title
   - ✅ Progress bar and charging details visible

### **Step 4: FCM Data Message Test**

#### **Option A: Using Firebase Console**
1. **Go to Firebase Console** → Your Project → Cloud Messaging
2. **Create a new message:**
   - Target: Single device
   - Token: Use your device's FCM token (check app logs)
   - Message type: Data message
   - Data payload:
     ```json
     {
       "type": "charging_complete",
       "session_id": "test_session_123",
       "title": "Charging Complete",
       "body": "Your EcoPlug charging session has finished"
     }
     ```

#### **Option B: Using Postman/API Testing**
```bash
POST https://fcm.googleapis.com/fcm/send
Headers:
  Authorization: key=YOUR_SERVER_KEY
  Content-Type: application/json

Body:
{
  "to": "DEVICE_FCM_TOKEN",
  "data": {
    "type": "charging_complete",
    "session_id": "test_123",
    "title": "FCM Icon Test",
    "body": "Testing FCM notification icons"
  }
}
```

3. **Verify FCM notification branding:**
   - ✅ Status bar shows identical monochrome EcoPlug icon
   - ✅ Expanded view shows identical full-color EcoPlug logo
   - ✅ Branding matches local notifications exactly

### **Step 5: Android Version Specific Testing**

#### **API 24 (Android 7.0) Testing:**
```bash
# Use Android 7.0 emulator or device
flutter run --target-platform android-arm64
```
- ✅ Notification channels work properly
- ✅ Icons render correctly on older notification system
- ✅ No legacy icon fallbacks visible

#### **API 29 (Android 10) Testing:**
```bash
# Use Android 10 emulator or device
flutter run --target-platform android-arm64
```
- ✅ Notification bubbles compatibility
- ✅ Proper icon rendering with new notification behaviors
- ✅ Dark theme automatic adaptation

#### **API 33 (Android 13) Testing:**
```bash
# Use Android 13+ emulator or device
flutter run --target-platform android-arm64
```
- ✅ Notification permission handling
- ✅ Themed icons compatibility (if enabled)
- ✅ Material You design system integration

### **Step 6: Dark/Light Mode Testing**

1. **Test in Light Mode:**
   - Go to Settings → Display → Dark theme → OFF
   - Trigger test notifications
   - ✅ Verify icon visibility and contrast

2. **Test in Dark Mode:**
   - Go to Settings → Display → Dark theme → ON
   - Check existing notifications
   - ✅ Verify icons adapt properly to dark theme
   - ✅ Ensure good contrast and visibility

3. **Dynamic Theme Switching:**
   - Keep a notification visible
   - Switch between dark/light mode
   - ✅ Icons should maintain visibility in both modes

### **Step 7: Lock Screen Testing**

1. **Enable lock screen notifications:**
   - Go to Settings → Notifications → On lock screen → "Show all notification content"

2. **Trigger test notification and lock device:**
   ```dart
   await tester.testLockScreenIcons();
   // Lock your device immediately
   ```

3. **Verify on lock screen:**
   - ✅ Notification appears on lock screen
   - ✅ EcoPlug branding clearly visible
   - ✅ Icons maintain proper contrast
   - ✅ Large icon shows in notification content

## 📋 **Verification Checklist**

### **Small Icon (Status Bar) Requirements:**
- [ ] Monochrome design (white/transparent)
- [ ] 24dp size, scalable vector format
- [ ] White tint applied by Android system
- [ ] Visible in both dark and light themes
- [ ] Consistent across all notification types
- [ ] No color information (system handles tinting)

### **Large Icon (Notification Content) Requirements:**
- [ ] Full-color EcoPlug logo visible
- [ ] Clear branding recognition
- [ ] Proper size and proportions
- [ ] Consistent across all notification types
- [ ] Visible on lock screen
- [ ] Good contrast in all modes

### **Cross-Platform Consistency:**
- [ ] Local notifications use same icons as FCM
- [ ] All notification services have identical branding
- [ ] No generic Android/Flutter icons visible
- [ ] Consistent behavior across API levels

### **User Experience Validation:**
- [ ] Icons are easily recognizable as EcoPlug
- [ ] Professional appearance in notification tray
- [ ] Clear distinction from other app notifications
- [ ] Maintains visibility in all Android themes

## 🔧 **Implementation Verification**

### **Current Icon Configuration:**
```dart
// Small monochrome icon for status bar
icon: '@drawable/ic_ecoplug_notification'

// Large full-color icon for notification content  
largeIcon: DrawableResourceAndroidBitmap('@mipmap/launcher_icon')

// Consistent color scheme
color: Color(0xFF4CAF50) // EcoPlug green
```

### **Files to Verify:**
1. **Small Icon:** `android/app/src/main/res/drawable/ic_ecoplug_notification.xml`
2. **Large Icons:** `android/app/src/main/res/mipmap-*/launcher_icon.png`
3. **Services Updated:**
   - `lib/services/active_charging_notification_service.dart`
   - `lib/services/fcm_service.dart`
   - `lib/services/welcome_notification_service.dart`
   - `lib/services/local_notification_manager.dart`

## 🚨 **Common Issues & Solutions**

### **Issue: Generic Android icon appears**
- **Cause:** Icon resource not found or incorrect path
- **Solution:** Verify `ic_ecoplug_notification.xml` exists in `drawable/` folder
- **Check:** Ensure icon path is `@drawable/ic_ecoplug_notification`

### **Issue: Large icon not showing**
- **Cause:** Launcher icon path incorrect or missing
- **Solution:** Verify `launcher_icon.png` files exist in all `mipmap-*` folders
- **Check:** Ensure path is `@mipmap/launcher_icon`

### **Issue: Icons not visible in dark mode**
- **Cause:** Icon doesn't have proper tint or contrast
- **Solution:** Ensure small icon uses `android:tint="@android:color/white"`
- **Check:** Test manually in both light and dark themes

### **Issue: FCM notifications have different icons**
- **Cause:** FCM service using different icon configuration
- **Solution:** Update FCM service to use same icon constants
- **Check:** Verify all services use centralized icon configuration

## 📱 **Device Recommendations for Testing**

### **Physical Devices:**
- Samsung Galaxy (OneUI) - API 29/33
- Google Pixel (Stock Android) - API 29/33  
- OnePlus/Xiaomi (Custom UI) - API 24/29
- Older device with API 24 for legacy testing

### **Emulators:**
- Android Studio AVD with API 24, 29, and 33
- Enable different screen densities (hdpi, xhdpi, xxhdpi)
- Test both tablet and phone form factors

## 🎯 **Success Criteria**

### **All tests pass when:**
1. ✅ Small icons are monochrome and properly tinted in status bar
2. ✅ Large icons show full-color EcoPlug logo in all notification types
3. ✅ FCM notifications have identical branding to local notifications
4. ✅ Icons work correctly on API 24, 29, and 33 devices
5. ✅ Dark and light mode both show proper icon visibility
6. ✅ Lock screen notifications display EcoPlug branding clearly
7. ✅ Multiple notifications maintain consistent icon appearance
8. ✅ No generic Android or Flutter icons are visible

## 📞 **Support & Debugging**

### **Enable Debug Logging:**
```dart
debugPrint('Icon configuration: ${NotificationIconHelper.getIconConfigurationSummary()}');
```

### **Verify Icon Resources:**
```bash
# Check if icon files exist
ls -la android/app/src/main/res/drawable/ic_ecoplug_notification.xml
ls -la android/app/src/main/res/mipmap-*/launcher_icon.png
```

### **Test FCM Token:**
```dart
final fcmToken = await FCMService().getToken();
debugPrint('FCM Token: $fcmToken');
```

---

**📱 Ready to test? Run the automated test script or follow the manual steps above to ensure your EcoPlug notifications have perfect icon branding across all Android versions and scenarios!**
