import 'package:flutter/material.dart';
import '../utils/app_themes.dart';

/// Transaction category enum for better type safety
enum TransactionCategory {
  addBalance,
  chargingPayment,
  chargingSession, // New category for charging session transactions (source: "txn")
  refund,
  transfer,
  cashback,
  failed,
  pending,
  unknown,
}

/// Utility class for mapping transaction data to appropriate icons and colors
class TransactionIconUtils {

  /// Icon data for each transaction category
  static const Map<TransactionCategory, IconData> _categoryIcons = {
    TransactionCategory.addBalance: Icons.account_balance_wallet,
    TransactionCategory.chargingPayment: Icons.electric_bolt,
    TransactionCategory.chargingSession: Icons.ev_station, // Distinct icon for charging sessions
    TransactionCategory.refund: Icons.refresh,
    TransactionCategory.transfer: Icons.swap_horiz,
    TransactionCategory.cashback: Icons.redeem,
    TransactionCategory.failed: Icons.cancel,
    TransactionCategory.pending: Icons.schedule,
    TransactionCategory.unknown: Icons.receipt,
  };

  /// Color schemes for each transaction category (light mode)
  static const Map<TransactionCategory, Color> _categoryColors = {
    TransactionCategory.addBalance: Color(0xFF4776E6),
    TransactionCategory.chargingPayment: Color(0xFF4CAF50),
    TransactionCategory.chargingSession: Color(0xFF1976D2), // Distinct blue color for charging sessions
    TransactionCategory.refund: Color(0xFF00C853),
    TransactionCategory.transfer: Color(0xFF2196F3),
    TransactionCategory.cashback: Color(0xFFE040FB),
    TransactionCategory.failed: Color(0xFFFF3D00),
    TransactionCategory.pending: Color(0xFFFF9800),
    TransactionCategory.unknown: Color(0xFF757575),
  };

  /// Determine transaction category based on transaction data
  static TransactionCategory getTransactionCategory({
    required String? type,
    required String? remark,
    required String? source,
    required String? status,
  }) {
    // Normalize strings for comparison
    final remarkLower = (remark ?? '').toLowerCase();
    final sourceLower = (source ?? '').toLowerCase();
    final statusLower = (status ?? '').toLowerCase();
    final typeLower = (type ?? '').toLowerCase();

    // Check for charging session transactions first (source: "txn")
    if (sourceLower == 'txn') {
      return TransactionCategory.chargingSession;
    }

    // Check for failed/rejected transactions
    if (statusLower.contains('failed') ||
        statusLower.contains('rejected') ||
        statusLower.contains('cancelled') ||
        remarkLower.contains('failed') ||
        remarkLower.contains('rejected') ||
        remarkLower.contains('cancelled')) {
      return TransactionCategory.failed;
    }

    // Check for pending transactions
    if (statusLower.contains('pending') ||
        statusLower.contains('processing') ||
        remarkLower.contains('pending')) {
      return TransactionCategory.pending;
    }

    // Check for refund transactions
    if (remarkLower.contains('refund') ||
        remarkLower.contains('reversal') ||
        remarkLower.contains('returned')) {
      return TransactionCategory.refund;
    }

    // Check for cashback/reward transactions
    if (remarkLower.contains('cashback') ||
        remarkLower.contains('reward') ||
        remarkLower.contains('bonus') ||
        remarkLower.contains('incentive')) {
      return TransactionCategory.cashback;
    }

    // Check for charging/payment transactions
    if (remarkLower.contains('charging') ||
        remarkLower.contains('payment') ||
        remarkLower.contains('ecoplug') ||
        remarkLower.contains('charger') ||
        remarkLower.contains('station') ||
        remarkLower.contains('ev') ||
        sourceLower.contains('charging') ||
        sourceLower.contains('payment')) {
      return TransactionCategory.chargingPayment;
    }

    // Check for balance addition transactions
    if (remarkLower.contains('balance added') ||
        remarkLower.contains('recharge') ||
        remarkLower.contains('wallet') ||
        remarkLower.contains('added') ||
        remarkLower.contains('top up') ||
        remarkLower.contains('deposit') ||
        (typeLower == 'cr' && remarkLower.contains('add'))) {
      return TransactionCategory.addBalance;
    }

    // Check for transfer transactions
    if (remarkLower.contains('transfer') ||
        remarkLower.contains('sent') ||
        remarkLower.contains('received') ||
        sourceLower.contains('transfer')) {
      return TransactionCategory.transfer;
    }

    // Default categorization based on transaction type
    if (typeLower == 'cr') {
      return TransactionCategory.addBalance; // Credit transactions default to balance addition
    } else if (typeLower == 'dr') {
      return TransactionCategory.chargingPayment; // Debit transactions default to charging payment
    }

    return TransactionCategory.unknown;
  }

  /// Get icon data for a transaction category
  static IconData getIcon(TransactionCategory category) {
    return _categoryIcons[category] ?? Icons.receipt;
  }

  /// Get icon data directly from transaction data
  static IconData getIconFromTransaction({
    required String? type,
    required String? remark,
    required String? source,
    required String? status,
  }) {
    final category = getTransactionCategory(
      type: type,
      remark: remark,
      source: source,
      status: status,
    );
    return getIcon(category);
  }

  /// Get color for a transaction category
  static Color getColor(TransactionCategory category, {bool isDarkMode = false}) {
    final baseColor = _categoryColors[category] ?? const Color(0xFF757575);

    // Adjust colors for dark mode if needed
    if (isDarkMode) {
      switch (category) {
        case TransactionCategory.addBalance:
          return AppThemes.primaryColor;
        case TransactionCategory.chargingPayment:
          return const Color(0xFF66BB6A);
        case TransactionCategory.chargingSession:
          return const Color(0xFF42A5F5); // Light blue for dark mode
        case TransactionCategory.refund:
          return const Color(0xFF26A69A);
        case TransactionCategory.transfer:
          return const Color(0xFF42A5F5);
        case TransactionCategory.cashback:
          return const Color(0xFFAB47BC);
        case TransactionCategory.failed:
          return const Color(0xFFEF5350);
        case TransactionCategory.pending:
          return const Color(0xFFFFB74D);
        case TransactionCategory.unknown:
          return const Color(0xFF90A4AE);
      }
    }

    return baseColor;
  }

  /// Get color directly from transaction data
  static Color getColorFromTransaction({
    required String? type,
    required String? remark,
    required String? source,
    required String? status,
    bool isDarkMode = false,
  }) {
    final category = getTransactionCategory(
      type: type,
      remark: remark,
      source: source,
      status: status,
    );
    return getColor(category, isDarkMode: isDarkMode);
  }

  /// Get background color for icon container
  static Color getBackgroundColor(TransactionCategory category, {bool isDarkMode = false}) {
    final baseColor = getColor(category, isDarkMode: isDarkMode);
    return baseColor.withValues(alpha: isDarkMode ? 0.2 : 0.1);
  }

  /// Get background color directly from transaction data
  static Color getBackgroundColorFromTransaction({
    required String? type,
    required String? remark,
    required String? source,
    required String? status,
    bool isDarkMode = false,
  }) {
    final category = getTransactionCategory(
      type: type,
      remark: remark,
      source: source,
      status: status,
    );
    return getBackgroundColor(category, isDarkMode: isDarkMode);
  }

  /// Get user-friendly category name
  static String getCategoryName(TransactionCategory category) {
    switch (category) {
      case TransactionCategory.addBalance:
        return 'Balance Added';
      case TransactionCategory.chargingPayment:
        return 'Charging Payment';
      case TransactionCategory.chargingSession:
        return 'Charging Session';
      case TransactionCategory.refund:
        return 'Refund';
      case TransactionCategory.transfer:
        return 'Transfer';
      case TransactionCategory.cashback:
        return 'Cashback';
      case TransactionCategory.failed:
        return 'Failed Transaction';
      case TransactionCategory.pending:
        return 'Pending Transaction';
      case TransactionCategory.unknown:
        return 'Transaction';
    }
  }

  /// Get category name directly from transaction data
  static String getCategoryNameFromTransaction({
    required String? type,
    required String? remark,
    required String? source,
    required String? status,
  }) {
    final category = getTransactionCategory(
      type: type,
      remark: remark,
      source: source,
      status: status,
    );
    return getCategoryName(category);
  }
}
