import 'package:flutter/material.dart';

/// App-wide color definitions
class AppColors {
  // Primary brand colors
  static const Color primary = Color(0xFF4CAF50); // Green
  static const Color secondary = Color(0xFF03A9F4); // Blue
  static const Color accent = Color(0xFF00BCD4); // Teal

  // Gradient colors for charging screen
  static const Color gradientStart = Color(0xFFE0F7FA); // Light blue
  static const Color gradientMiddle = Color(0xFFB2DFDB); // Light teal
  static const Color gradientEnd = Color(0xFFE0F2F1); // Very light teal

  // Charging progress colors
  static const Color progressStart = Color(0xFF8BC34A); // Light green
  static const Color progressMiddle = Color(0xFF4CAF50); // Medium green
  static const Color progressEnd = Color(0xFF009688); // Teal
  static const Color progressBackground = Color(0xFFECEFF1); // Very light gray

  // UI Element colors
  static const Color cardBackground = Colors.white;
  static const Color textPrimary = Color(0xFF212121); // Near black
  static const Color textSecondary = Color(0xFF757575); // Dark gray
  static const Color textLight = Color(0xFFBDBDBD); // Light gray
  static const Color divider = Color(0xFFEEEEEE); // Very light gray

  // Backward compatibility aliases
  static const Color primaryColor = primary;
  static const Color textColor = textPrimary;
  static const Color textSecondaryColor = textSecondary;
  static const Color borderColor = divider;
  static const Color chipBackgroundColor = Color(0xFFF5F5F5); // Light gray

  // Status colors
  static const Color success = Color(0xFF4CAF50); // Green
  static const Color error = Color(0xFFE53935); // Red
  static const Color warning = Color(0xFFFFC107); // Amber
  static const Color info = Color(0xFF2196F3); // Blue

  // Environmental impact highlight
  static const Color ecoGreen = Color(0xFF4CAF50); // Green
  static const Color ecoGreenDark = Color(0xFF2E7D32); // Dark green
  static const Color ecoHighlight = Color(0xFFAED581); // Light green
}
