# Firebase Cloud Messaging (FCM) & Local Notifications Implementation Guide

## 📋 Overview

This guide covers the complete implementation of Firebase Cloud Messaging (FCM) and Local Notifications in your EcoPlug Flutter app. The implementation provides:

- ✅ **Firebase Cloud Messaging (FCM)** - Push notifications from server
- ✅ **Local Notifications** - App-generated notifications
- ✅ **Unified Notification Service** - Single interface for all notifications
- ✅ **Riverpod Integration** - State management for notifications
- ✅ **Cross-platform Support** - Android and iOS compatibility
- ✅ **Background Message Handling** - Works when app is closed
- ✅ **Notification Navigation** - Deep linking from notifications

## 🚀 What's Been Implemented

### 1. Dependencies Added
```yaml
# Firebase Cloud Messaging dependencies
firebase_core: ^3.6.0  # Firebase core functionality
firebase_messaging: ^15.1.3  # Firebase Cloud Messaging
```

### 2. Files Created/Modified

#### New Files:
- `lib/firebase_options.dart` - Firebase configuration
- `lib/services/fcm_service.dart` - FCM service implementation
- `lib/services/unified_notification_service.dart` - Unified notification management
- `lib/providers/notification_provider.dart` - Riverpod notification provider
- `lib/screens/settings/notification_settings_screen.dart` - Settings UI
- `android/app/google-services.json` - Android Firebase config (placeholder)
- `ios/Runner/GoogleService-Info.plist` - iOS Firebase config (placeholder)

#### Modified Files:
- `pubspec.yaml` - Added Firebase dependencies
- `lib/main.dart` - Firebase initialization
- `android/app/build.gradle.kts` - Added Google Services plugin
- `android/settings.gradle.kts` - Added Google Services plugin
- `ios/Runner/Info.plist` - Added remote notification background mode

### 3. Services Implemented

#### FCMService
- FCM token management
- Message handling (foreground/background/terminated)
- Topic subscription/unsubscription
- Integration with local notifications
- Background message handler

#### UnifiedNotificationService
- Single interface for all notification operations
- Manages both FCM and local notifications
- User preference handling
- Topic management based on user settings

### 4. Features

#### Push Notifications (FCM)
- Server-to-device messaging
- Topic-based notifications
- User-specific notifications
- Background message handling
- Automatic token refresh

#### Local Notifications
- Charging session progress notifications
- Scheduled notifications
- Interactive notifications
- Custom notification channels

#### Notification Management
- User preference settings
- Topic subscription management
- Notification navigation/deep linking
- Service status monitoring

## 🔧 Setup Instructions

### Step 1: Firebase Project Setup

1. **Create Firebase Project**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Create a new project or use existing one
   - Enable Cloud Messaging

2. **Add Android App**
   - Package name: `com.eeil.ecoplug`
   - Download `google-services.json`
   - Replace the placeholder file in `android/app/google-services.json`

3. **Add iOS App**
   - Bundle ID: `com.eeil.ecoplug`
   - Download `GoogleService-Info.plist`
   - Replace the placeholder file in `ios/Runner/GoogleService-Info.plist`

4. **Update Firebase Options**
   - Run `flutterfire configure` to generate proper `firebase_options.dart`
   - Or manually update the placeholder values in `lib/firebase_options.dart`

### Step 2: Install Dependencies

```bash
flutter pub get
```

### Step 3: Platform-Specific Setup

#### Android
- ✅ Google Services plugin added to build.gradle files
- ✅ Notification permissions configured in AndroidManifest.xml
- ✅ FCM service configured in AndroidManifest.xml

#### iOS
- ✅ Remote notification background mode added to Info.plist
- ✅ Notification permissions configured
- Add `GoogleService-Info.plist` to Xcode project:
  1. Open `ios/Runner.xcworkspace` in Xcode
  2. Right-click on `Runner` folder
  3. Select "Add Files to Runner"
  4. Choose `GoogleService-Info.plist`
  5. Ensure it's added to the Runner target

### Step 4: Backend Integration

#### Send FCM Token to Your Backend
```dart
// Example API call to send token
final notificationService = UnifiedNotificationService();
final token = await notificationService.getFCMToken();

// Send token to your backend
await apiService.updateUserFCMToken(userId, token);
```

#### Send Push Notifications from Backend
```json
// Example FCM message format
{
  "to": "FCM_TOKEN_HERE",
  "notification": {
    "title": "Charging Complete",
    "body": "Your EV is fully charged!"
  },
  "data": {
    "type": "charging_complete",
    "session_id": "session_123",
    "station_id": "station_456"
  }
}
```

## 📱 Usage Examples

### Initialize Notifications
```dart
// In main.dart - already implemented
final notificationService = UnifiedNotificationService();
await notificationService.initialize();
```

### Subscribe to Topics
```dart
// Subscribe to user-specific topics
await notificationService.subscribeToUserTopics(userId);

// Subscribe to location-based topics
await notificationService.subscribeToLocationTopics("Mumbai", "Maharashtra");
```

### Show Local Notifications
```dart
// Show charging notification
await notificationService.showChargingNotification(
  title: "Charging in Progress",
  body: "Your EV is charging at 50%",
  chargePercentage: 0.5,
  isCharging: true,
  stationName: "EcoPlug Station",
);
```

### Using with Riverpod
```dart
// In your widget
class MyWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationState = ref.watch(notificationProvider);
    final fcmToken = ref.watch(fcmTokenProvider);
    
    return Column(
      children: [
        Text('FCM Token: ${fcmToken.when(
          data: (token) => token ?? 'No token',
          loading: () => 'Loading...',
          error: (e, _) => 'Error: $e',
        )}'),
        // ... rest of your UI
      ],
    );
  }
}
```

## 🔍 Testing

### Test Local Notifications
```dart
// Show test notification
final notificationService = UnifiedNotificationService();
await notificationService.showTestNotification();
```

### Test FCM
1. Use Firebase Console to send test messages
2. Use the notification settings screen to view FCM token
3. Send test messages using FCM token

### Debug Information
- Check console logs for initialization status
- Use notification settings screen to view service status
- Monitor FCM token changes

## 🎯 Notification Types Supported

### 1. Charging Notifications
- Session start/stop
- Progress updates
- Completion alerts
- Error notifications

### 2. Station Notifications
- Availability alerts
- Maintenance updates
- New station announcements

### 3. Trip Notifications
- Trip reminders
- Route updates
- Traffic alerts

### 4. Wallet Notifications
- Payment confirmations
- Low balance alerts
- Transaction updates

### 5. General Notifications
- App updates
- Promotional offers
- System announcements

## 🔧 Customization

### Notification Channels (Android)
Modify `fcm_service.dart` to add custom notification channels:

```dart
const AndroidNotificationChannel customChannel = AndroidNotificationChannel(
  'custom_channel_id',
  'Custom Channel Name',
  description: 'Description of the channel',
  importance: Importance.high,
  // ... other properties
);
```

### Notification Actions
Add interactive buttons to notifications:

```dart
const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
  'channel_id',
  'channel_name',
  actions: [
    AndroidNotificationAction(
      'action_id',
      'Action Label',
    ),
  ],
);
```

## 🚨 Important Notes

1. **Firebase Configuration**: Replace placeholder configuration files with actual Firebase project files
2. **Permissions**: Ensure notification permissions are granted on both platforms
3. **Background Processing**: iOS has strict limitations on background processing
4. **Token Management**: FCM tokens can change, ensure your backend handles token updates
5. **Testing**: Test on physical devices for accurate notification behavior

## 📚 Additional Resources

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Flutter Local Notifications Plugin](https://pub.dev/packages/flutter_local_notifications)
- [Firebase Flutter Setup](https://firebase.flutter.dev/docs/overview)

## 🎉 Conclusion

Your EcoPlug app now has a comprehensive notification system that supports:
- ✅ Push notifications from your backend via FCM
- ✅ Local notifications generated by the app
- ✅ User preference management
- ✅ Topic-based subscriptions
- ✅ Background message handling
- ✅ Deep linking and navigation
- ✅ Cross-platform compatibility

The implementation is production-ready and follows Flutter best practices. Remember to replace the placeholder Firebase configuration files with your actual project configuration before deploying to production.
