# Firebase Integration Verification Guide

## 📋 Overview

This guide helps you verify that the Firebase Cloud Messaging (FCM) integration is working correctly with your production Firebase project configuration.

## ✅ **Configuration Updated Successfully**

### **Android Configuration (✅ Complete)**
- **google-services.json**: Updated with production Firebase project
- **Project ID**: `ecoplug-9ab21`
- **Project Number**: `************`
- **Package Name**: `com.eeil.ecoplug` ✅ (matches app configuration)
- **Android App ID**: `1:************:android:7e19a2e8f607c0584eab2f`
- **API Key**: `AIzaSyAdQ49tgN61V6taM4PJHYOD2pdmKkwMjW4`

### **Firebase Options (✅ Updated)**
- **firebase_options.dart**: Updated with production configuration
- **Android settings**: Complete and ready for testing
- **Web settings**: Updated with project details
- **iOS settings**: Prepared with placeholders (see iOS requirements below)

## 🧪 **Testing Your Firebase Integration**

### **Step 1: Run Firebase Configuration Test**

Add the Firebase test widget to any screen in your app:

```dart
import 'package:ecoplug/widgets/firebase_config_test_widget.dart';

// Add to any screen for testing
FirebaseConfigTestWidget()
```

### **Step 2: Verify Configuration**

1. **Open the test widget** in your app
2. **Click "Verify Config"** to run comprehensive tests
3. **Check the overall status**:
   - ✅ **Green**: Configuration is working perfectly
   - ⚠️ **Orange**: Configuration has warnings (usually permissions)
   - ❌ **Red**: Configuration has errors that need fixing

### **Step 3: Test FCM Token Generation**

1. **Click "Get FCM Token"** in the test widget
2. **Verify token is generated** (should be 150+ characters)
3. **Copy the token** for backend testing
4. **Test token refresh** to ensure tokens can be renewed

### **Step 4: Test Welcome Notifications**

Use the existing welcome notification test widget:

```dart
import 'package:ecoplug/widgets/welcome_notification_test_widget.dart';

// Test welcome notifications
WelcomeNotificationTestWidget()
```

## 📱 **Testing FCM Notifications**

### **Method 1: Using Firebase Console**

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Select your project**: `ecoplug-9ab21`
3. **Navigate to**: Cloud Messaging → Send your first message
4. **Enter notification details**:
   - **Title**: "Test FCM Notification"
   - **Body**: "Testing Firebase Cloud Messaging integration"
5. **Target**: Single device
6. **FCM Token**: Use the token from your test widget
7. **Send the message**

### **Method 2: Using Backend API**

Ask your backend team to send a test notification using this format:

```json
{
  "to": "YOUR_FCM_TOKEN_HERE",
  "notification": {
    "title": "Test from Backend",
    "body": "FCM integration is working!"
  },
  "data": {
    "type": "test_notification",
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

### **Method 3: Using curl Command**

```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "YOUR_FCM_TOKEN",
    "notification": {
      "title": "Test Notification",
      "body": "Testing FCM integration"
    }
  }'
```

## 🍎 **iOS Configuration Requirements**

### **What You Need from Backend Team**

Request the following information for iOS support:

1. **iOS API Key**: Different from Android API key
2. **iOS App ID**: Format `1:************:ios:XXXXXXXXXXXXXXXX`
3. **iOS Client ID**: Format `XXXXXXXXX.apps.googleusercontent.com`
4. **Reversed Client ID**: Format `com.googleusercontent.apps.XXXXXXXXX`

### **iOS Configuration Steps**

Once you receive the iOS configuration:

1. **Update GoogleService-Info.plist**:
   ```xml
   <key>API_KEY</key>
   <string>YOUR_IOS_API_KEY</string>
   <key>GOOGLE_APP_ID</key>
   <string>1:************:ios:YOUR_IOS_APP_ID</string>
   <key>CLIENT_ID</key>
   <string>YOUR_CLIENT_ID.apps.googleusercontent.com</string>
   <key>REVERSED_CLIENT_ID</key>
   <string>com.googleusercontent.apps.YOUR_REVERSED_CLIENT_ID</string>
   ```

2. **Update firebase_options.dart**:
   ```dart
   static const FirebaseOptions ios = FirebaseOptions(
     apiKey: 'YOUR_IOS_API_KEY',
     appId: '1:************:ios:YOUR_IOS_APP_ID',
     messagingSenderId: '************',
     projectId: 'ecoplug-9ab21',
     storageBucket: 'ecoplug-9ab21.appspot.com',
     iosBundleId: 'com.eeil.ecoplug',
   );
   ```

## 🔧 **Backend Integration Requirements**

### **What Backend Team Needs to Implement**

1. **FCM Token Storage**:
   ```json
   POST /api/user/fcm-token
   {
     "user_id": "string",
     "fcm_token": "string",
     "platform": "android|ios",
     "app_version": "string"
   }
   ```

2. **Send Notifications**:
   ```json
   POST /api/notifications/send
   {
     "user_ids": ["string"],
     "title": "string",
     "body": "string",
     "data": {},
     "notification_type": "string"
   }
   ```

3. **Topic Management**:
   ```json
   POST /api/notifications/subscribe
   {
     "user_id": "string",
     "topics": ["charging_updates", "station_alerts"]
   }
   ```

### **Firebase Server Key**

Backend team needs the **Firebase Server Key** for sending notifications:
1. **Go to**: Firebase Console → Project Settings → Cloud Messaging
2. **Copy**: Server key (legacy) or use Firebase Admin SDK

## 🚨 **Troubleshooting**

### **Common Issues and Solutions**

#### **1. FCM Token Not Generated**
- **Check**: Firebase initialization in main.dart
- **Verify**: google-services.json is in correct location
- **Ensure**: Google Services plugin is applied in build.gradle

#### **2. Notifications Not Received**
- **Test**: Token generation first
- **Check**: Notification permissions
- **Verify**: App is in correct state (foreground/background/terminated)
- **Confirm**: Server key is correct

#### **3. Package Name Mismatch**
- **Verify**: Package name in google-services.json matches build.gradle
- **Current**: `com.eeil.ecoplug` (✅ verified)

#### **4. Build Errors**
- **Run**: `flutter clean && flutter pub get`
- **Check**: Google Services plugin version compatibility
- **Verify**: Firebase dependencies are latest versions

### **Debug Commands**

```bash
# Clean and rebuild
flutter clean
flutter pub get

# Check Firebase configuration
flutter run --debug

# View detailed logs
flutter logs
```

## 📊 **Verification Checklist**

### **Android (Primary Platform)**
- ✅ google-services.json updated with production config
- ✅ Package name matches: `com.eeil.ecoplug`
- ✅ firebase_options.dart updated
- ✅ Google Services plugin configured
- ⏳ FCM token generation (test required)
- ⏳ Notification delivery (test required)
- ⏳ Welcome notifications (test required)

### **iOS (Secondary Platform)**
- ⏳ iOS configuration needed from backend team
- ⏳ GoogleService-Info.plist to be updated
- ⏳ firebase_options.dart iOS section to be completed
- ⏳ iOS testing after configuration

### **Backend Integration**
- ⏳ FCM token storage endpoint
- ⏳ Notification sending capability
- ⏳ Topic subscription management
- ⏳ Server key configuration

## 🎯 **Next Steps**

### **Immediate Actions**
1. **Test Android configuration** using the Firebase test widget
2. **Verify FCM token generation** works correctly
3. **Test notification delivery** in all app states
4. **Request iOS configuration** from backend team

### **Backend Coordination**
1. **Share FCM token** with backend team for testing
2. **Provide notification payload formats** for different types
3. **Test end-to-end notification flow** from backend to app
4. **Set up topic-based notifications** for user preferences

### **Production Readiness**
1. **Complete iOS configuration** when available
2. **Test notification delivery** in production environment
3. **Monitor FCM token refresh** and backend synchronization
4. **Implement notification analytics** and error tracking

## 📞 **Support**

If you encounter issues:

1. **Check the Firebase test widget** for detailed error messages
2. **Review Firebase Console** for project configuration
3. **Verify package names** match exactly
4. **Test with simple notifications** before complex flows
5. **Contact backend team** for server-side configuration issues

The Firebase integration is now configured and ready for testing with your production Firebase project!
