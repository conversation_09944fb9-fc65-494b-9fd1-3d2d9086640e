# 🌐 EcoPlug Connectivity Error System

## Overview

The EcoPlug app now includes a comprehensive connectivity error handling system that displays a beautiful Lottie animation when internet connectivity issues occur. This system provides a consistent user experience across the app when network problems arise.

## Features

- ✨ **Lottie Animation**: Beautiful error animation from `assets/data/erorr Animation.json`
- 🎨 **Theme-Aware**: Automatically adapts to light/dark mode
- 🔄 **Retry Mechanism**: Built-in retry functionality with connectivity checking
- 📱 **Multiple Display Options**: Full-page, bottom sheet, or modal presentations
- 🛠 **Easy Integration**: Simple utility methods and mixins for quick implementation
- 🎯 **Smart Detection**: Automatically detects connectivity-related errors

## Components

### 1. ConnectivityErrorPage
Main error page widget that displays the Lottie animation and error message.

**Location**: `lib/screens/error/connectivity_error_page.dart`

**Features**:
- Lottie animation display
- Customizable error messages
- Retry button with loading state
- Connection tips section
- Smooth animations (fade and slide)

### 2. ConnectivityErrorService
Service class for handling connectivity error detection and navigation.

**Location**: `lib/services/connectivity_error_service.dart`

**Key Methods**:
- `isConnectivityError()` - Detects if an error is connectivity-related
- `getConnectivityErrorMessage()` - Provides user-friendly error messages
- `showConnectivityError()` - Shows error page
- `showConnectivityErrorSheet()` - Shows error as bottom sheet
- `handleApiCall()` - Wraps API calls with automatic error handling

### 3. ConnectivityErrorUtils
Extension methods and utilities for easy integration.

**Location**: `lib/utils/connectivity_error_utils.dart`

**Features**:
- Extension methods on `BuildContext`
- `ConnectivityErrorMixin` for widgets
- Pre-defined error scenarios
- Utility methods for common use cases

## Usage Examples

### Basic Error Page Display

```dart
import 'package:ecoplug/utils/connectivity_error_utils.dart';

// Show error page
context.showConnectivityError(
  message: 'Custom error message',
  onRetry: () {
    // Handle retry logic
    Navigator.of(context).pop();
    _retryOperation();
  },
);
```

### Bottom Sheet Display

```dart
// Show as bottom sheet
context.showConnectivityErrorSheet(
  message: 'Connection error occurred',
  onRetry: () {
    Navigator.of(context).pop();
    _retryApiCall();
  },
);
```

### Named Route Navigation

```dart
// Navigate using named route
context.pushConnectivityError(
  errorMessage: 'Server unreachable',
  showBackButton: true,
);
```

### API Call with Automatic Error Handling

```dart
try {
  final result = await context.handleApiCall(() async {
    return await apiService.fetchData();
  }, onRetry: () {
    Navigator.of(context).pop();
    _fetchData(); // Retry the operation
  });
} catch (e) {
  // Error was handled automatically
}
```

### Using the Mixin

```dart
class MyWidget extends StatefulWidget {
  // ...
}

class _MyWidgetState extends State<MyWidget> 
    with ConnectivityErrorMixin {
  
  Future<void> _loadData() async {
    await executeWithConnectivityHandling(() async {
      return await apiService.loadData();
    }, retryCallback: _loadData);
  }
}
```

### Connectivity Check Before Operation

```dart
final result = await context.withConnectivityCheck(() async {
  // This will only execute if connectivity is available
  return await performNetworkOperation();
});

if (result != null) {
  // Operation completed successfully
}
```

## Integration with Existing Error Handler

The system integrates with the existing `ErrorHandler` class:

```dart
import 'package:ecoplug/shared/utils/error_handler.dart';

try {
  await apiCall();
} catch (error) {
  await ErrorHandler.handleError(
    context,
    error,
    onRetry: () => _retryOperation(),
    showAsSheet: true,
  );
}
```

## Error Scenarios

The system automatically detects these connectivity-related errors:

- **Network timeouts**: Connection timeout, request timeout
- **DNS issues**: Host lookup failed, DNS resolution errors
- **Connection problems**: Connection refused, connection reset
- **Socket exceptions**: Network unreachable, connection failed
- **HTTP status codes**: 408, 502, 503, 504, 522, 523, 524
- **Offline states**: No internet connection, device offline

## Pre-defined Error Messages

Use `ConnectivityErrorScenarios` for common error messages:

```dart
import 'package:ecoplug/utils/connectivity_error_utils.dart';

context.showConnectivityError(
  message: ConnectivityErrorScenarios.apiTimeout,
);

// Available scenarios:
// - apiTimeout
// - serverUnreachable
// - networkError
// - offline
// - slowConnection
// - dnsError
// - connectionRefused
// - sslError
```

## Route Configuration

The error page is available via named route:

```dart
Navigator.of(context).pushNamed(
  '/connectivity-error',
  arguments: {
    'error_message': 'Custom error message',
    'show_back_button': true,
  },
);
```

## Assets Configuration

The Lottie animation file is configured in `pubspec.yaml`:

```yaml
flutter:
  assets:
    - assets/data/erorr Animation.json
```

## Best Practices

1. **Use the mixin** for widgets that frequently handle network operations
2. **Wrap API calls** with `handleApiCall()` for automatic error handling
3. **Check connectivity** before expensive operations using `withConnectivityCheck()`
4. **Provide retry callbacks** to allow users to easily retry failed operations
5. **Use bottom sheets** for non-critical errors, full pages for critical ones
6. **Customize error messages** to provide context-specific information

## Testing

Use the example widget to test different scenarios:

```dart
import 'package:ecoplug/examples/connectivity_error_example.dart';

// Navigate to the example page to test all functionality
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => const ConnectivityErrorExample(),
  ),
);
```

## Theme Integration

The error page automatically adapts to the app's theme using:

- `AppThemes.darkBackground` / `Colors.white` for backgrounds
- `AppThemes.darkTextPrimary` / `AppThemes.lightTextPrimary` for text
- `AppThemes.primaryColor` for buttons and accents
- Proper contrast ratios for accessibility

## Performance Considerations

- Lottie animations are cached automatically
- Connectivity checks are optimized with the existing `ConnectivityService`
- Error detection uses efficient string matching
- UI animations are lightweight and performant

## Future Enhancements

- [ ] Add more Lottie animations for different error types
- [ ] Implement offline data caching integration
- [ ] Add analytics tracking for connectivity issues
- [ ] Create automated retry with exponential backoff
- [ ] Add network quality indicators
