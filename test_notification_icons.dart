import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:ecoplug/services/active_charging_notification_service.dart';
import 'package:ecoplug/services/fcm_service.dart';
import 'package:ecoplug/services/welcome_notification_service.dart';
import 'package:ecoplug/services/local_notification_manager.dart';
import 'package:ecoplug/models/charging_session.dart';

/// Comprehensive Notification Icon Testing Script for EcoPlug
/// Tests notification icon rendering across multiple Android versions and scenarios
/// ⚠️ DEBUG MODE ONLY - This file contains test notifications and should not be used in production
class NotificationIconTester {
  static final NotificationIconTester _instance =
      NotificationIconTester._internal();
  factory NotificationIconTester() => _instance;
  NotificationIconTester._internal();

  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  final ActiveChargingNotificationService _chargingService =
      ActiveChargingNotificationService();
  final FCMService _fcmService = FCMService();
  final WelcomeNotificationService _welcomeService =
      WelcomeNotificationService();
  final LocalNotificationManager _localManager = LocalNotificationManager();

  bool _isInitialized = false;

  /// Initialize the notification icon tester
  /// ⚠️ DEBUG MODE ONLY - Will not initialize in production builds
  Future<void> initialize() async {
    if (!kDebugMode) {
      debugPrint('❌ Notification Icon Tester: Debug mode only');
      return;
    }

    if (_isInitialized) return;

    debugPrint('🧪 ===== INITIALIZING NOTIFICATION ICON TESTER =====');

    try {
      // Initialize all notification services
      await _chargingService.initialize();
      await _fcmService.initialize();
      await _welcomeService.initialize();
      await _localManager.initialize();

      _isInitialized = true;
      debugPrint('✅ Notification icon tester initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing notification icon tester: $e');
      rethrow;
    }
  }

  /// Test 1: Local Charging Notification Icon Rendering
  Future<void> testChargingNotificationIcons() async {
    debugPrint('🧪 ===== TEST 1: CHARGING NOTIFICATION ICONS =====');

    await initialize();

    try {
      // Create test charging session
      final testSession = ChargingSession(
        id: 'ICON_TEST_${DateTime.now().millisecondsSinceEpoch}',
        stationUid: 'ICON_TEST_STATION',
        connectorId: 'CONNECTOR_1',
        startTime: DateTime.now().subtract(const Duration(minutes: 10)),
        currentCharge: 0.45, // 45%
        currentPower: 7.2,
        energyDelivered: 1.2,
        cost: 18.50,
        co2Saved: 0.8,
      );

      // Start charging notification
      await _chargingService.startChargingSessionNotification(testSession);

      debugPrint(
          '✅ CHARGING NOTIFICATION SENT - CHECK ANDROID NOTIFICATION TRAY');
      debugPrint('🔍 VERIFY:');
      debugPrint('   📱 Status bar shows monochrome white-tinted EcoPlug icon');
      debugPrint('   📱 Expanded view shows full-color EcoPlug logo');
      debugPrint('   📱 Notification title: "Charging Active • 45%"');
      debugPrint('   📱 Shows charging progress bar and details');

      // Keep notification visible for inspection
      await Future.delayed(const Duration(seconds: 5));
    } catch (e) {
      debugPrint('❌ Error testing charging notification icons: $e');
    }
  }

  /// Test 2: Welcome Notification Icon Rendering
  Future<void> testWelcomeNotificationIcons() async {
    debugPrint('🧪 ===== TEST 2: WELCOME NOTIFICATION ICONS =====');

    await initialize();

    try {
      // Send welcome notification
      await _welcomeService.showWelcomeNotification(
        userName: 'Test User',
        isFirstLogin: false,
      );

      debugPrint(
          '✅ WELCOME NOTIFICATION SENT - CHECK ANDROID NOTIFICATION TRAY');
      debugPrint('🔍 VERIFY:');
      debugPrint('   📱 Status bar shows monochrome white-tinted EcoPlug icon');
      debugPrint('   📱 Expanded view shows full-color EcoPlug logo');
      debugPrint('   📱 Welcome message with EcoPlug branding');

      await Future.delayed(const Duration(seconds: 3));
    } catch (e) {
      debugPrint('❌ Error testing welcome notification icons: $e');
    }
  }

  /// Test 3: Simulate FCM Data Message (Local Test)
  Future<void> testFCMStyleNotificationIcons() async {
    debugPrint('🧪 ===== TEST 3: FCM-STYLE NOTIFICATION ICONS =====');

    await initialize();

    try {
      // Create FCM-style notification using local notifications
      const androidDetails = AndroidNotificationDetails(
        'fcm_test_channel',
        'FCM Test Channel',
        channelDescription:
            'Test FCM-style notifications with EcoPlug branding',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@drawable/ic_ecoplug_notification', // Small monochrome icon
        largeIcon: DrawableResourceAndroidBitmap(
            '@mipmap/launcher_icon'), // Large full-color icon
        color: Color(0xFF4CAF50),
        colorized: true,
        styleInformation: BigTextStyleInformation(
          'This simulates an FCM data message notification. Check that the small icon is monochrome and the large icon shows the full EcoPlug logo.',
          htmlFormatBigText: false,
          contentTitle: 'FCM Icon Test - Charging Complete',
          htmlFormatContentTitle: false,
          summaryText: 'EcoPlug Notification',
        ),
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        9001,
        'FCM Icon Test - Charging Complete',
        'Your charging session has completed successfully. Check notification icons.',
        notificationDetails,
        payload: 'fcm_icon_test',
      );

      debugPrint(
          '✅ FCM-STYLE NOTIFICATION SENT - CHECK ANDROID NOTIFICATION TRAY');
      debugPrint('🔍 VERIFY:');
      debugPrint('   📱 Status bar shows monochrome white-tinted EcoPlug icon');
      debugPrint('   📱 Expanded view shows full-color EcoPlug logo');
      debugPrint('   📱 Identical branding to charging notifications');

      await Future.delayed(const Duration(seconds: 3));
    } catch (e) {
      debugPrint('❌ Error testing FCM-style notification icons: $e');
    }
  }

  /// Test 4: Multiple Notification Types Simultaneously
  Future<void> testMultipleNotificationTypes() async {
    debugPrint('🧪 ===== TEST 4: MULTIPLE NOTIFICATION TYPES =====');

    await initialize();

    try {
      // Send multiple notifications to test consistency
      await testChargingNotificationIcons();
      await Future.delayed(const Duration(seconds: 2));

      await testWelcomeNotificationIcons();
      await Future.delayed(const Duration(seconds: 2));

      await testFCMStyleNotificationIcons();

      debugPrint(
          '✅ MULTIPLE NOTIFICATIONS SENT - CHECK ANDROID NOTIFICATION TRAY');
      debugPrint('🔍 VERIFY:');
      debugPrint('   📱 All notifications show consistent EcoPlug branding');
      debugPrint('   📱 Status bar icons are all monochrome white-tinted');
      debugPrint('   📱 Expanded view icons all show full-color EcoPlug logo');
      debugPrint('   📱 No generic Android/Flutter icons visible');
    } catch (e) {
      debugPrint('❌ Error testing multiple notification types: $e');
    }
  }

  /// Test 5: Dark/Light Mode Behavior
  Future<void> testDarkLightModeIcons() async {
    debugPrint('🧪 ===== TEST 5: DARK/LIGHT MODE ICON BEHAVIOR =====');

    await initialize();

    try {
      // Send test notification for mode testing
      const androidDetails = AndroidNotificationDetails(
        'mode_test_channel',
        'Mode Test Channel',
        channelDescription: 'Test notification icons in dark/light modes',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@drawable/ic_ecoplug_notification',
        largeIcon: DrawableResourceAndroidBitmap('@mipmap/launcher_icon'),
        color: Color(0xFF4CAF50),
        colorized: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
      );

      await _localNotifications.show(
        9002,
        'Dark/Light Mode Icon Test',
        'Switch between dark and light mode to test icon visibility',
        notificationDetails,
        payload: 'mode_test',
      );

      debugPrint('✅ MODE TEST NOTIFICATION SENT');
      debugPrint('🔍 MANUAL TEST STEPS:');
      debugPrint('   1. Check notification in current mode');
      debugPrint(
          '   2. Switch Android theme (Settings > Display > Dark theme)');
      debugPrint('   3. Check notification again in opposite mode');
      debugPrint(
          '   4. Verify icons are visible and properly tinted in both modes');
      debugPrint('');
      debugPrint('🔍 VERIFY:');
      debugPrint('   📱 Small icon adapts to system theme (white-tinted)');
      debugPrint('   📱 Large icon remains consistently visible');
      debugPrint('   📱 Good contrast in both dark and light modes');
    } catch (e) {
      debugPrint('❌ Error testing dark/light mode icons: $e');
    }
  }

  /// Test 6: Lock Screen Notification Icons
  Future<void> testLockScreenIcons() async {
    debugPrint('🧪 ===== TEST 6: LOCK SCREEN NOTIFICATION ICONS =====');

    await initialize();

    try {
      // Send high-priority notification that will appear on lock screen
      const androidDetails = AndroidNotificationDetails(
        'lockscreen_test_channel',
        'Lock Screen Test Channel',
        channelDescription: 'Test notification icons on lock screen',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@drawable/ic_ecoplug_notification',
        largeIcon: DrawableResourceAndroidBitmap('@mipmap/launcher_icon'),
        color: Color(0xFF4CAF50),
        colorized: true,
        visibility:
            NotificationVisibility.public, // Ensure it shows on lock screen
        fullScreenIntent: false,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
      );

      await _localNotifications.show(
        9003,
        'Lock Screen Icon Test - EcoPlug',
        'Lock your device to test notification icons on lock screen',
        notificationDetails,
        payload: 'lockscreen_test',
      );

      debugPrint('✅ LOCK SCREEN TEST NOTIFICATION SENT');
      debugPrint('🔍 MANUAL TEST STEPS:');
      debugPrint('   1. Lock your Android device');
      debugPrint('   2. Check notification on lock screen');
      debugPrint('   3. Verify icon visibility and branding');
      debugPrint('');
      debugPrint('🔍 VERIFY:');
      debugPrint('   📱 Notification appears on lock screen');
      debugPrint('   📱 EcoPlug branding is clearly visible');
      debugPrint('   📱 Icons maintain proper contrast on lock screen');
    } catch (e) {
      debugPrint('❌ Error testing lock screen icons: $e');
    }
  }

  /// Run all notification icon tests
  Future<void> runAllIconTests() async {
    debugPrint('🧪 ===== RUNNING ALL NOTIFICATION ICON TESTS =====');
    debugPrint(
        '🧪 Device Info: Android API ${Platform.operatingSystemVersion}');
    debugPrint('');

    try {
      await testChargingNotificationIcons();
      await Future.delayed(const Duration(seconds: 3));

      await testWelcomeNotificationIcons();
      await Future.delayed(const Duration(seconds: 3));

      await testFCMStyleNotificationIcons();
      await Future.delayed(const Duration(seconds: 3));

      await testDarkLightModeIcons();
      await Future.delayed(const Duration(seconds: 3));

      await testLockScreenIcons();
      await Future.delayed(const Duration(seconds: 3));

      await testMultipleNotificationTypes();

      debugPrint('');
      debugPrint('✅ ===== ALL NOTIFICATION ICON TESTS COMPLETED =====');
      debugPrint('');
      debugPrint('📋 MANUAL VERIFICATION CHECKLIST:');
      debugPrint('   ✓ Small icons are monochrome, white-tinted in status bar');
      debugPrint(
          '   ✓ Large icons show full-color EcoPlug logo in expanded view');
      debugPrint('   ✓ FCM-style notifications have identical branding');
      debugPrint('   ✓ Icons work properly in dark and light modes');
      debugPrint('   ✓ Lock screen notifications show proper branding');
      debugPrint('   ✓ Multiple notifications maintain consistent branding');
      debugPrint('');
      debugPrint('📱 Check your Android notification tray and lock screen!');
    } catch (e) {
      debugPrint('❌ Error running notification icon tests: $e');
    }
  }

  /// Clean up test notifications
  Future<void> cleanupTestNotifications() async {
    debugPrint('🧹 Cleaning up test notifications...');

    try {
      // Cancel all test notifications
      await _localNotifications.cancel(9001); // FCM test
      await _localNotifications.cancel(9002); // Mode test
      await _localNotifications.cancel(9003); // Lock screen test

      // Stop charging notification if running
      await _chargingService.stopChargingSessionNotification();

      debugPrint('✅ Test notifications cleaned up');
    } catch (e) {
      debugPrint('❌ Error cleaning up test notifications: $e');
    }
  }

  /// Print device and API information
  void printDeviceInfo() {
    debugPrint('📱 ===== DEVICE TESTING INFORMATION =====');
    debugPrint('📱 Platform: ${Platform.operatingSystem}');
    debugPrint('📱 Version: ${Platform.operatingSystemVersion}');
    debugPrint('📱 Locale: ${Platform.localeName}');
    debugPrint('');
    debugPrint('🎯 TARGET API LEVELS FOR TESTING:');
    debugPrint('   • API 24 (Android 7.0) - Nougat');
    debugPrint('   • API 29 (Android 10) - Q');
    debugPrint('   • API 33 (Android 13) - Tiramisu');
    debugPrint('');
    debugPrint('🔍 NOTIFICATION ICON REQUIREMENTS:');
    debugPrint('   • Small icon: Monochrome, white-tinted, 24dp');
    debugPrint('   • Large icon: Full-color EcoPlug logo');
    debugPrint('   • Consistent branding across all notification types');
    debugPrint('   • Dark/light mode compatibility');
    debugPrint('   • Lock screen visibility');
    debugPrint('');
  }
}

/// Extension methods for easy testing
extension NotificationIconTestingExtension on BuildContext {
  /// Quick access to run notification icon tests
  Future<void> testNotificationIcons() async {
    final tester = NotificationIconTester();
    tester.printDeviceInfo();
    await tester.runAllIconTests();
  }

  /// Quick access to cleanup test notifications
  Future<void> cleanupNotificationTests() async {
    final tester = NotificationIconTester();
    await tester.cleanupTestNotifications();
  }
}
