# Profile API GSTIN Data Retrieval Test

## Test Objective
Verify that GSTIN data is properly retrieved from the profile API endpoint and correctly processed for display and storage.

## API Response Structure
The profile API (`/user/profile`) returns data in this format:
```json
{
    "data": {
        "id": 4,
        "uid": "d874c43d-da1b-4c17-95c7-a3e97a361dd8",
        "name": "Demo",
        "email": "<EMAIL>",
        "address": "Ecoplug energy India ltd",
        "state": "Rajasthan",
        "pincode": "301001",
        "mobile_number": "9999999992",
        "status": 1,
        "source": "eeil.online",
        "domain": "eeil.online",
        "company_uid": "c8794eac-6f93-4d3f-901b-************",
        "created_at": "2024-11-11T05:41:51.000000Z",
        "updated_at": "2025-06-24T04:52:33.000000Z",
        "instant_charging": 0,
        "rfid_code": [
            "143B17C3",
            "FABE",
            "23A240D"
        ],
        "rfid_status": 1,
        "vids": [],
        "autocharge_status": 0,
        "tariff_id": "304884c3-b269-4297-9288-a8e689482110",
        "type": "user",
        "fleet_admin_id": 0,
        "gst_no": "08 AAICE 0887 G1ZF",
        "business_name": "Ecoplug",
        "max_amp_limit": 90,
        "balance": {
            "balance": 1140.81
        },
        "vehicle": 0,
        "profile": {
            "vehicle": 0
        }
    },
    "fleetuser": [],
    "message": "Ok",
    "success": true
}
```

## Implementation Details

### **Enhanced Profile Data Loading**
The `_loadProfileData()` method now implements a robust data retrieval strategy:

1. **Primary Source**: Fresh data from `/user/profile` API endpoint
2. **Fallback 1**: AuthManager cached data if API fails
3. **Fallback 2**: Final fallback with error handling

### **GST Data Extraction Process**
```dart
// Extract GST data directly from the API response data
final profileData = profileResponse.data;

// Clean GST number by removing spaces for consistent storage
_gstNo = profileData.gstNo != null && profileData.gstNo!.isNotEmpty
    ? GSTFormatter.cleanGSTForAPI(profileData.gstNo!)
    : null;
_businessName = profileData.businessName;
```

### **Space Removal Implementation**
- **Input**: `"08 AAICE 0887 G1ZF"` (with spaces from API)
- **Processing**: `GSTFormatter.cleanGSTForAPI()`
- **Output**: `"08AAICE0887G1ZF"` (clean 15-character string)

## Test Scenarios

### **Scenario 1: User with GST Data**
**API Response**: Contains `gst_no` and `business_name`
```json
{
  "gst_no": "08 AAICE 0887 G1ZF",
  "business_name": "Ecoplug"
}
```

**Expected Behavior**:
- ✅ GST number cleaned: `"08AAICE0887G1ZF"`
- ✅ Business name preserved: `"Ecoplug"`
- ✅ `_hasGSTData()` returns `true`
- ✅ Complete Business Information section visible
- ✅ GSTInputWidget shows current values in collapsed state

### **Scenario 2: User with Partial GST Data**
**API Response**: Contains only `business_name`
```json
{
  "gst_no": null,
  "business_name": "Ecoplug"
}
```

**Expected Behavior**:
- ✅ GST number: `null`
- ✅ Business name: `"Ecoplug"`
- ✅ `_hasGSTData()` returns `true` (business name exists)
- ✅ Business Information section visible

### **Scenario 3: User without GST Data**
**API Response**: No GST data
```json
{
  "gst_no": null,
  "business_name": null
}
```

**Expected Behavior**:
- ✅ GST number: `null`
- ✅ Business name: `null`
- ✅ `_hasGSTData()` returns `false`
- ✅ Business Information section hidden
- ✅ "Add GST Details" button visible

### **Scenario 4: API Failure Fallback**
**API Response**: Request fails or times out

**Expected Behavior**:
- ✅ Falls back to AuthManager cached data
- ✅ Extracts GST data from cached userData map
- ✅ Applies same space cleaning logic
- ✅ UI updates based on available data

## Debug Logging

The implementation includes comprehensive logging:
```dart
debugPrint('🔍 GST DATA LOADED FROM API:');
debugPrint('  Original GST: ${profileData.gstNo}');
debugPrint('  Cleaned GST: $_gstNo');
debugPrint('  Business Name: $_businessName');
debugPrint('  Has GST Data: ${_hasGSTData()}');
```

## Verification Steps

### **Manual Testing**
1. **Open Edit Profile Page**
   - Check console logs for API data loading
   - Verify GST data extraction and cleaning

2. **User with GST Data**
   - Confirm Business Information section is visible
   - Verify GSTInputWidget shows cleaned GST number
   - Test expand/collapse functionality

3. **User without GST Data**
   - Confirm Business Information section is hidden
   - Verify "Add GST Details" button is visible
   - Test adding new GST information

4. **API Failure Simulation**
   - Disconnect internet during profile loading
   - Verify fallback to cached data works
   - Check error handling and recovery

### **API Response Validation**
1. **Check Network Tab**
   - Verify `/user/profile` endpoint is called
   - Confirm response contains expected GST fields
   - Validate response structure matches expected format

2. **Data Processing Verification**
   - Input: `"08 AAICE 0887 G1ZF"`
   - Expected Output: `"08AAICE0887G1ZF"`
   - Verify no spaces in processed GST number

## Success Criteria

### ✅ **API Data Retrieval**
- Profile API endpoint called successfully
- GST data extracted from correct response structure
- Fallback mechanisms work when API fails

### ✅ **Space Removal**
- All spaces removed from GST numbers
- 15-character clean format maintained
- Consistent processing across all data sources

### ✅ **Conditional Visibility**
- Business section shows when GST data exists
- Business section hides when no GST data
- Proper state management during data loading

### ✅ **Error Handling**
- Graceful fallback to cached data
- Comprehensive error logging
- UI remains functional during failures

## Summary

The enhanced profile data loading implementation ensures:

1. **Fresh Data**: Always attempts to get latest data from API
2. **Space Cleaning**: Consistent removal of spaces from GST numbers
3. **Robust Fallbacks**: Multiple layers of error handling
4. **Conditional Display**: Proper show/hide logic based on data presence
5. **Debug Support**: Comprehensive logging for troubleshooting

The implementation correctly handles the provided API response structure and ensures GSTIN data is properly retrieved, cleaned, and displayed in the edit profile interface.
