# FCM Subscription Test Guide

## 🔔 **OVERVIEW**

This guide provides comprehensive testing instructions to verify that both **charging_id** and **test_1** FCM topic subscriptions work correctly in the EcoPlug app.

## 🧪 **TEST FILES CREATED**

### **1. Automated Test Suite**
- `test/fcm_subscription_test.dart` - Comprehensive Flutter test suite
- `test_fcm_subscriptions.dart` - Command-line test script
- `run_fcm_test.bat` - Windows test runner
- `run_fcm_test.sh` - Linux/Mac test runner

### **2. Manual Test Widget**
- `lib/debug/fcm_test_topic_subscription_test.dart` - Interactive test interface

## 🚀 **HOW TO RUN TESTS**

### **Option 1: Command Line Test (Recommended)**

**Windows:**
```cmd
run_fcm_test.bat
```

**Linux/Mac:**
```bash
./run_fcm_test.sh
```

**Manual:**
```cmd
dart test_fcm_subscriptions.dart
```

### **Option 2: Flutter Test Suite**
```cmd
flutter test test/fcm_subscription_test.dart --verbose
```

### **Option 3: Manual Testing in App**
1. Navigate to the test widget: `FCMTestTopicSubscriptionTest`
2. Use the test buttons to verify subscriptions
3. Check comprehensive status display

## 🔍 **WHAT THE TESTS VERIFY**

### **Test 1: test_1 Topic Subscription**
- ✅ Automatic subscription during login
- ✅ FirebaseMessaging.instance.subscribeToTopic('test_1') call
- ✅ Comprehensive debugging and error handling
- ✅ Status tracking and persistence
- ✅ Retry logic on failure

### **Test 2: charging_id Topic Subscription**
- ✅ Subscription during charging session start
- ✅ Topic format: `Charging_[charging_id]`
- ✅ FCM subscription service integration
- ✅ Error handling and retry logic

### **Test 3: Combined Functionality**
- ✅ Both subscriptions work together
- ✅ No conflicts between subscriptions
- ✅ Proper session management

### **Test 4: Error Handling**
- ✅ Timeout handling (15 seconds for initial, 10 for retry)
- ✅ Retry logic with 3-second delay
- ✅ Comprehensive error logging
- ✅ Status tracking for failed attempts

### **Test 5: Data Persistence**
- ✅ Subscription status persists across app restarts
- ✅ SharedPreferences storage working correctly
- ✅ Comprehensive tracking data saved

## 📊 **EXPECTED TEST RESULTS**

### **Successful Output Should Show:**
```
🔔 ===== FCM SUBSCRIPTION VERIFICATION TEST =====
🔔 ✅ Flutter test passed successfully!
🔔 ✅ auth_notification_service.dart exists
🔔   ✅ Contains test_1 subscription implementation
🔔 ✅ fcm_subscription_service.dart exists
🔔   ✅ Contains charging_id subscription implementation
🔔 ✅ Firebase messaging import found
🔔 ✅ test_1 subscription integrated into login flow
🔔 ✅ Comprehensive debugging implemented
🔔 ✅ Error handling and retry logic implemented
🔔 ✅ Status tracking implemented
🔔 ✅ Charging topic format implemented correctly
🔔 🎉 ALL FCM SUBSCRIPTIONS SUCCESSFUL! 🎉
```

## 🔧 **MANUAL VERIFICATION IN APP**

### **1. Login Test (test_1 subscription)**
1. Log into your EcoPlug app
2. Check debug console for these logs:
```
🔔 ===== FCM TEST_1 TOPIC SUBSCRIPTION START =====
🔔 Step 1: Checking Firebase Messaging availability...
🔔 ✅ Firebase Messaging instance obtained
🔔 Step 2: Getting FCM token for verification...
🔔 ✅ FCM Token available: [token_preview]...
🔔 Step 4: Attempting FCM topic subscription...
🔔 ✅ FCM subscription call completed successfully
🔔 ✅ Successfully subscribed to FCM topic: test_1
```

### **2. Charging Test (charging_id subscription)**
1. Start a charging session in your app
2. Check debug console for these logs:
```
🔔 ===== SUBSCRIBING TO CHARGING NOTIFICATIONS (FCM TOPICS ONLY) =====
🔔 Charging ID: [your_charging_id]
🔔 Subscribing to FCM topic: Charging_[your_charging_id]
🔔 ✅ Successfully subscribed to FCM topic: Charging_[your_charging_id]
```

### **3. Using Test Widget**
1. Navigate to `FCMTestTopicSubscriptionTest` widget
2. Tap "Test Subscription" - should show success
3. Tap "Simulate Login" - should trigger test_1 subscription
4. Check "Comprehensive Subscription Status" for detailed info
5. Use "Clear Data" to reset and test again

## 🐛 **TROUBLESHOOTING**

### **If test_1 subscription fails:**
- Check Firebase configuration
- Verify internet connection
- Look for error logs with 🔔 prefix
- Check FCM token availability
- Verify app permissions for notifications

### **If charging_id subscription fails:**
- Check FCMSubscriptionService initialization
- Verify charging session flow
- Check for topic format issues
- Look for retry attempt logs

### **Common Issues:**
1. **Firebase not initialized**: Ensure Firebase is properly set up
2. **No internet**: FCM requires network connection
3. **Permissions**: Check notification permissions
4. **Token issues**: FCM token might not be available

## 📱 **INTEGRATION VERIFICATION**

### **Login Flow Integration:**
```
User Login → AuthNotificationService.onLoginSuccess() → _subscribeToUserNotifications() → _subscribeToTestTopic() → FirebaseMessaging.instance.subscribeToTopic('test_1')
```

### **Charging Flow Integration:**
```
Start Charging → FCMSubscriptionService.subscribeToChargingNotifications() → FirebaseMessaging.instance.subscribeToTopic('Charging_[id]')
```

## ✅ **SUCCESS CRITERIA**

Both subscriptions are working correctly if:

1. **test_1 subscription:**
   - ✅ Triggers automatically on login
   - ✅ Shows success logs in console
   - ✅ Status tracking shows `is_subscribed: true`
   - ✅ No error logs or retry attempts

2. **charging_id subscription:**
   - ✅ Triggers when charging starts
   - ✅ Uses correct topic format `Charging_[id]`
   - ✅ Returns `true` from subscription method
   - ✅ Shows success in debug logs

3. **Overall system:**
   - ✅ Both work independently
   - ✅ Both work together without conflicts
   - ✅ Error handling works correctly
   - ✅ Status tracking persists data

## 🎯 **NEXT STEPS AFTER TESTING**

1. **If tests pass:** Your FCM subscriptions are working correctly!
2. **Send test messages:** Use Firebase Console to send test messages to both topics
3. **Monitor in production:** Watch for subscription logs in production builds
4. **Backend integration:** Ensure your backend sends messages to the correct topics

---

**Both charging_id and test_1 FCM topic subscriptions are now fully implemented and tested! 🎉**
