import 'package:flutter/material.dart';
import 'package:payu_checkoutpro_flutter/payu_checkoutpro_flutter.dart';
import '../services/payment/payu_service.dart';

/// Debug helper class for PayU integration testing
class PayUDebugHelper {
  /// Test PayU SDK initialization
  static Future<bool> testSDKInitialization({
    required String merchantKey,
    required String environment,
  }) async {
    debugPrint('🧪 PAYU DEBUG: ========== SDK INITIALIZATION TEST ==========');

    try {
      final result = await PayUService.init(
        merchantKey: merchantKey,
        environment: environment,
        enableLogging: true,
      );

      debugPrint('🧪 PAYU DEBUG: SDK initialization result: $result');

      if (result) {
        debugPrint('✅ PAYU DEBUG: SDK initialization SUCCESSFUL');
      } else {
        debugPrint('❌ PAYU DEBUG: SDK initialization FAILED');
      }

      return result;
    } catch (e) {
      debugPrint('❌ PAYU DEBUG: SDK initialization EXCEPTION: $e');
      return false;
    }
  }

  /// Test callback registration
  static void testCallbackRegistration() {
    debugPrint('🧪 PAYU DEBUG: ========== CALLBACK REGISTRATION TEST ==========');

    // Get the PayU service instance
    final instance = PayUService.instance;
    debugPrint('🧪 PAYU DEBUG: PayU service instance: $instance');
    debugPrint('🧪 PAYU DEBUG: Instance type: ${instance.runtimeType}');

    // Test if the instance implements the protocol
    debugPrint('🧪 PAYU DEBUG: Instance implements PayUCheckoutProProtocol: ${instance is PayUCheckoutProProtocol}');
  }

  /// Validate payment parameters
  static Map<String, bool> validatePaymentParameters(Map<String, dynamic> params) {
    debugPrint('🧪 PAYU DEBUG: ========== PAYMENT PARAMETERS VALIDATION ==========');

    final requiredParams = [
      'key', 'txnid', 'amount', 'productinfo', 'firstname', 'email', 'phone', 'surl', 'furl'
    ];

    final validation = <String, bool>{};

    for (String param in requiredParams) {
      final isValid = params.containsKey(param) &&
                     params[param] != null &&
                     params[param].toString().isNotEmpty;

      validation[param] = isValid;

      if (isValid) {
        debugPrint('✅ PAYU DEBUG: PARAM VALID: $param = ${params[param]}');
      } else {
        debugPrint('❌ PAYU DEBUG: PARAM INVALID/MISSING: $param');
      }
    }

    // Check optional but important parameters
    final optionalParams = ['hash', 'environment', 'userCredential'];
    for (String param in optionalParams) {
      if (params.containsKey(param)) {
        debugPrint('ℹ️ PAYU DEBUG: OPTIONAL PARAM: $param = ${params[param]}');
      } else {
        debugPrint('⚠️ PAYU DEBUG: OPTIONAL PARAM MISSING: $param');
      }
    }

    final allValid = validation.values.every((valid) => valid);
    debugPrint('🧪 PAYU DEBUG: All required parameters valid: $allValid');

    return validation;
  }

  /// Test response tracking system
  static void testResponseTracking() {
    debugPrint('🧪 PAYU DEBUG: ========== RESPONSE TRACKING TEST ==========');

    final testTxnId = 'test_${DateTime.now().millisecondsSinceEpoch}';

    // Initialize tracking
    PayUService.initializeResponseTracking(testTxnId);
    debugPrint('🧪 PAYU DEBUG: Initialized tracking for: $testTxnId');

    // Test different callback types
    final callbackTypes = ['SUCCESS', 'FAILURE', 'CANCELLATION', 'ERROR'];

    for (String type in callbackTypes) {
      final isHandled = PayUService.isResponseAlreadyHandled(type);
      debugPrint('🧪 PAYU DEBUG: $type already handled: $isHandled');

      if (!isHandled) {
        PayUService.markResponseHandled(type);
        debugPrint('🧪 PAYU DEBUG: Marked $type as handled');

        // Test duplicate detection
        final isDuplicate = PayUService.isResponseAlreadyHandled(type);
        debugPrint('🧪 PAYU DEBUG: $type duplicate check: $isDuplicate');
      }
    }

    // Reset for next test
    PayUService.resetForTesting();
    debugPrint('🧪 PAYU DEBUG: Reset tracking for next test');
  }

  /// Simulate callback execution
  static void simulateCallbacks() {
    debugPrint('🧪 PAYU DEBUG: ========== CALLBACK SIMULATION ==========');

    final testResponse = {
      'status': 'success',
      'txnid': 'test_txn_123',
      'amount': '100.00',
      'productinfo': 'Test Product',
    };

    try {
      // Get PayU service instance
      final service = PayUService.instance;

      debugPrint('🧪 PAYU DEBUG: Simulating onPaymentSuccess callback...');
      service.onPaymentSuccess(testResponse);

      debugPrint('🧪 PAYU DEBUG: Simulating onPaymentFailure callback...');
      service.onPaymentFailure({'status': 'failed', 'error': 'Test error'});

      debugPrint('🧪 PAYU DEBUG: Simulating onPaymentCancel callback...');
      service.onPaymentCancel({'status': 'cancelled'});

      debugPrint('🧪 PAYU DEBUG: Simulating onError callback...');
      service.onError({'error': 'Test error'});

    } catch (e) {
      debugPrint('❌ PAYU DEBUG: Callback simulation error: $e');
    }
  }

  /// Generate test payment parameters
  static Map<String, dynamic> generateTestPaymentParams({
    required String merchantKey,
    required String environment,
    double amount = 100.0,
  }) {
    final txnId = 'test_${DateTime.now().millisecondsSinceEpoch}';

    return {
      'key': merchantKey,
      'txnid': txnId,
      'amount': amount.toString(),
      'productinfo': 'Test Payment',
      'firstname': 'Test User',
      'email': '<EMAIL>',
      'phone': '9999999999',
      'surl': 'com.eeil.ecoplug://payu/success',
      'furl': 'com.eeil.ecoplug://payu/failure',
      'environment': environment,
      'userCredential': '$merchantKey:test_user',
      'udf1': 'test_data',
      'udf2': 'debug_mode',
      'udf3': '',
      'udf4': '',
      'udf5': '',
    };
  }

  /// Run comprehensive PayU debug test
  static Future<void> runComprehensiveTest({
    required String merchantKey,
    required String environment,
  }) async {
    debugPrint('🧪 PAYU DEBUG: ========== COMPREHENSIVE DEBUG TEST START ==========');

    // Test 1: SDK Initialization
    final sdkInitialized = await testSDKInitialization(
      merchantKey: merchantKey,
      environment: environment,
    );

    if (!sdkInitialized) {
      debugPrint('❌ PAYU DEBUG: SDK initialization failed - stopping tests');
      return;
    }

    // Test 2: Callback Registration
    testCallbackRegistration();

    // Test 3: Payment Parameters
    final testParams = generateTestPaymentParams(
      merchantKey: merchantKey,
      environment: environment,
    );
    validatePaymentParameters(testParams);

    // Test 4: Response Tracking
    testResponseTracking();

    // Test 5: Callback Simulation
    simulateCallbacks();

    debugPrint('🧪 PAYU DEBUG: ========== COMPREHENSIVE DEBUG TEST END ==========');
  }
}
