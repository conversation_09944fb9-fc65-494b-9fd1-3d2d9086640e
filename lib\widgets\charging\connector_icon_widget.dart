import 'package:flutter/material.dart';

class ConnectorIconWidget extends StatelessWidget {
  final String connectorType;
  final double size;
  final Color? color;

  const ConnectorIconWidget({
    super.key,
    required this.connectorType,
    this.size = 24.0,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: _getConnectorIcon(),
    );
  }

  Widget _getConnectorIcon() {
    String iconPath = 'assets/images/connector_icons/';

    // Determine the correct connector icon based on type
    switch (connectorType.toLowerCase()) {
      case 'ccs2':
      case 'ccs':
      case 'ccs combo':
      case 'ccs_combo':
        iconPath += 'ccs2.png';
        break;
      case 'chademo':
        iconPath += 'chademo.png';
        break;
      case 'type2':
      case 'type 2':
        iconPath += 'type2.png';
        break;
      case 'gbt':
      case 'gb/t':
        iconPath += 'gbt.png';
        break;
      default:
        iconPath += 'generic.png';
    }

    return Image.asset(
      iconPath,
      width: size,
      height: size,
      color: color,
    );
  }
}
