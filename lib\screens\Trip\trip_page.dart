import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:ecoplug/screens/dashboard/google_map_widget.dart'; // Import the GoogleMapWidget
import 'package:ecoplug/models/station.dart';
import 'package:ecoplug/screens/station/station_details_page.dart';
import 'package:ecoplug/utils/app_theme.dart';

import 'package:ecoplug/providers/theme_provider.dart';
import 'package:ecoplug/config/google_maps_styles.dart';
import 'package:ecoplug/widgets/location_search_widget.dart';
import 'package:ecoplug/widgets/route_alternatives_selector.dart';
import 'package:ecoplug/models/place_suggestion.dart';
import 'package:ecoplug/models/destination_station.dart';
import 'package:ecoplug/providers/places_provider.dart';
import 'package:ecoplug/providers/route_provider.dart';
import 'package:ecoplug/providers/trip_markers_provider.dart';

import '../../utils/app_themes.dart';
// Animations may be used in the future

class TripPage extends ConsumerStatefulWidget {
  const TripPage({super.key});

  @override
  TripPageState createState() => TripPageState();
}

// In the TripPageState class, add:
class TripPageState extends ConsumerState<TripPage>
    with TickerProviderStateMixin {
  // Controller for the Google Map
  GoogleMapController? _mapController;

  // Controller for the draggable scrollable sheet
  final DraggableScrollableController _draggableScrollableController =
      DraggableScrollableController();

  // Flag to determine whether to use the MapWidget or GoogleMap
  final bool useMapWidget = true; // Set to true to use your MapWidget

  // Initial camera position set to a placeholder location
  final CameraPosition _initialCameraPosition = const CameraPosition(
    target: LatLng(
      28.6139,
      77.2090,
    ), // Example: New Delhi (replace with start location)
    zoom: 12,
  );

  // Removed: _stationsAlongRoute and _isLoadingStations - now using route provider
  // Removed: _stationService - not used in current implementation

  // Example markers for charging stations - NO INFO WINDOWS TO PREVENT POPUPS
  final Set<Marker> _stationMarkers = {
    Marker(
      markerId: MarkerId('station1'),
      position: LatLng(28.6200, 77.2100),
      // REMOVED: InfoWindow to prevent annoying popups
    ),
    Marker(
      markerId: MarkerId('station2'),
      position: LatLng(28.6250, 77.2150),
      // REMOVED: InfoWindow to prevent annoying popups
    ),
    Marker(
      markerId: MarkerId('station3'),
      position: LatLng(28.6300, 77.2200),
      // REMOVED: InfoWindow to prevent annoying popups
    ),
  };

  // Booleans to track bottom sheet state and navigation mode
  bool _isSheetExpanded = false;
  bool _isNavigating = false;

  // Local loading state for immediate UI feedback
  bool _isLoadingStationsLocally = false;

  // Flag to track if we've already expanded the sheet for current route calculation
  bool _hasExpandedSheetForCurrentRoute = false;

  // Flag to track if we've already positioned camera for current route
  bool _hasPositionedCameraForCurrentRoute = false;

  // TextEditingControllers for start location and destination
  final TextEditingController _startLocationController =
      TextEditingController(text: 'Start Location');
  final TextEditingController _destinationController = TextEditingController();

  // Selected locations with coordinates
  PlaceSuggestion? _selectedStartLocation;
  PlaceSuggestion? _selectedDestination;

  // Animation controllers
  late AnimationController _mapControlsAnimationController;
  late AnimationController _stationListAnimationController;
  late AnimationController _animationController;
  late AnimationController _searchBarAnimationController;
  late Animation<double> _searchBarAnimation;

  // Scroll controller for station list
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Removed: _loadStationsAlongRoute() - now using route provider

    // Add debugging for initial state
    debugPrint('🚀 TRIP PAGE: === INITIALIZATION DEBUG ===');
    debugPrint(
        '🚀 TRIP PAGE: Start location controller: "${_startLocationController.text}"');
    debugPrint(
        '🚀 TRIP PAGE: Destination controller: "${_destinationController.text}"');
    debugPrint(
        '🚀 TRIP PAGE: Selected start location: $_selectedStartLocation');
    debugPrint('🚀 TRIP PAGE: Selected destination: $_selectedDestination');
    debugPrint('🚀 TRIP PAGE: === END INITIALIZATION DEBUG ===');

    // Initialize animation controllers
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _mapControlsAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _stationListAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _searchBarAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _searchBarAnimation = CurvedAnimation(
      parent: _searchBarAnimationController,
      curve: Curves.easeOutCubic,
    );

    // Start the animations when the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _animationController.forward();
      _searchBarAnimationController.forward();
      _mapControlsAnimationController.forward();
      _stationListAnimationController.forward();
    });
  }

  // Removed: _loadStationsAlongRoute() - now using route provider for destination stations

  @override
  void dispose() {
    _mapController?.dispose();
    _startLocationController.dispose();
    _destinationController.dispose();
    _animationController.dispose();
    _searchBarAnimationController.dispose();
    _mapControlsAnimationController.dispose();
    _stationListAnimationController.dispose();
    scrollController.dispose();
    // Clear trip markers when leaving the page
    ref.read(tripMarkersProvider.notifier).clearMarkers();
    super.dispose();
  }

  // Called when the Google Map is created
  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
  }

  // Calculate route between selected locations using Google Directions API
  Future<void> _calculateRoute() async {
    debugPrint('🚀 === _calculateRoute() METHOD CALLED ===');

    // Enhanced coordinate validation and parameter formatting
    if (_selectedStartLocation?.coordinates == null ||
        _selectedDestination?.coordinates == null) {
      debugPrint('❌ ROUTE: Missing coordinates for route calculation');
      debugPrint(
          '❌ ROUTE: Start location: ${_selectedStartLocation?.coordinates}');
      debugPrint('❌ ROUTE: Destination: ${_selectedDestination?.coordinates}');

      // Clear loading state if coordinates are invalid
      if (mounted && _isLoadingStationsLocally) {
        setState(() {
          _isLoadingStationsLocally = false;
        });
      }
      return;
    }

    // Properly format coordinates for API calls
    final origin = _selectedStartLocation!.coordinates!;
    final destination = _selectedDestination!.coordinates!;

    debugPrint('🗺️ ROUTE: === STARTING ROUTE CALCULATION ===');
    debugPrint(
        '🗺️ ROUTE: Origin: $origin (${_selectedStartLocation!.mainText})');
    debugPrint(
        '🗺️ ROUTE: Destination: $destination (${_selectedDestination!.mainText})');
    debugPrint(
        '🗺️ ROUTE: Distance between points: ${_calculateDistance(origin, destination).toStringAsFixed(2)} km');

    // Clear any existing route state first to prevent stale data
    debugPrint(
        '🗺️ ROUTE: Clearing existing route state and all related data...');
    ref.read(routeProvider.notifier).clearRoute();
    ref.read(tripMarkersProvider.notifier).clearMarkers();
    debugPrint('🗺️ ROUTE: All previous route data cleared successfully');

    // Set up UI state for route calculation
    setState(() {
      _isSheetExpanded = true;
    });
    _expandSheetToFullHeightForLoading();

    try {
      // Use direct await pattern for route calculation
      debugPrint('🗺️ ROUTE: Calling calculateRouteAlternatives...');
      await ref.read(routeProvider.notifier).calculateRouteAlternatives(
            origin: origin,
            destination: destination,
          );

      debugPrint('🗺️ ROUTE: ✅ Route calculation completed');

      // Check if route calculation was successful
      final routeState = ref.read(routeProvider);
      if (routeState.hasRoute && mounted) {
        debugPrint('🗺️ ROUTE: ✅ Route available, handling completion');

        // Clear loading state
        if (_isLoadingStationsLocally) {
          setState(() {
            _isLoadingStationsLocally = false;
          });
        }

        // Handle route completion and camera positioning
        _handleRouteCompletion();
      } else if (routeState.error != null) {
        throw Exception(routeState.error);
      }
    } catch (e) {
      debugPrint('🗺️ ROUTE: ❌ Route alternatives failed: $e');

      // Fallback to simple route calculation
      try {
        debugPrint(
            '🗺️ ROUTE: Attempting fallback to simple route calculation...');
        await ref.read(routeProvider.notifier).calculateRoute(
              origin: origin,
              destination: destination,
            );

        debugPrint('🗺️ ROUTE: ✅ Fallback route calculation completed');

        final fallbackRouteState = ref.read(routeProvider);
        if (fallbackRouteState.hasRoute && mounted) {
          debugPrint(
              '🗺️ ROUTE: ✅ Fallback route available, handling completion');

          // Clear loading state
          if (_isLoadingStationsLocally) {
            setState(() {
              _isLoadingStationsLocally = false;
            });
          }

          // Handle route completion and camera positioning
          _handleRouteCompletion();
        } else if (fallbackRouteState.error != null) {
          throw Exception(fallbackRouteState.error);
        }
      } catch (fallbackError) {
        debugPrint(
            '🗺️ ROUTE: ❌ Fallback route calculation failed: $fallbackError');

        // Clear loading state on error
        if (mounted && _isLoadingStationsLocally) {
          setState(() {
            _isLoadingStationsLocally = false;
          });
        }

        // Show error to user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Route calculation failed: $fallbackError'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    }

    debugPrint('🚀 === END _calculateRoute() METHOD ===');
  }

  // Helper method to calculate distance between two points
  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    final double lat1Rad = point1.latitude * (math.pi / 180);
    final double lat2Rad = point2.latitude * (math.pi / 180);
    final double deltaLatRad =
        (point2.latitude - point1.latitude) * (math.pi / 180);
    final double deltaLngRad =
        (point2.longitude - point1.longitude) * (math.pi / 180);

    final double a = math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
        math.cos(lat1Rad) *
            math.cos(lat2Rad) *
            math.sin(deltaLngRad / 2) *
            math.sin(deltaLngRad / 2);
    final double c = 2 * math.asin(math.sqrt(a));

    return earthRadius * c;
  }

  // Method to handle coordinated sheet expansion and camera positioning
  void _handleRouteCalculationStart() {
    debugPrint(
        '🚀 ROUTE START: Handling coordinated route calculation start workflow');

    // Reset flags for new route calculation
    _hasExpandedSheetForCurrentRoute = false;
    _hasPositionedCameraForCurrentRoute = false;

    // Step 1: Immediately expand sheet to full height for route calculation with loading feedback
    debugPrint(
        '🚀 ROUTE START: Step 1 - Expanding sheet for loading visibility');
    _expandSheetToFullHeightForLoading();

    // Route completion is now handled directly in _calculateRoute() method
  }

  // Method to expand sheet to full height for loading state
  void _expandSheetToFullHeightForLoading() {
    if (_hasExpandedSheetForCurrentRoute) {
      debugPrint('📋 SHEET: Already expanded for current route, skipping');
      return;
    }

    debugPrint(
        '📋 SHEET: Expanding to full height immediately for route calculation with enhanced loading feedback...');

    // Immediate expansion with enhanced animation
    if (mounted && _draggableScrollableController.isAttached) {
      _draggableScrollableController.animateTo(
        0.90, // Increased to 90% for better loading visibility
        duration: const Duration(
            milliseconds: 300), // Faster expansion for immediate feedback
        curve: Curves.easeOutCubic, // Snappier curve for loading state
      );
      setState(() {
        _hasExpandedSheetForCurrentRoute = true;
        _isSheetExpanded = true;
      });
      debugPrint(
          '📋 SHEET: ✅ Successfully expanded to 90% height for enhanced loading visibility');
    } else {
      // Fallback with minimal delay if controller not ready
      debugPrint('📋 SHEET: Controller not ready, using fallback expansion...');
      Future.delayed(const Duration(milliseconds: 30), () {
        if (mounted && _draggableScrollableController.isAttached) {
          _draggableScrollableController.animateTo(
            0.90,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOutCubic,
          );
          setState(() {
            _hasExpandedSheetForCurrentRoute = true;
            _isSheetExpanded = true;
          });
          debugPrint(
              '📋 SHEET: ✅ Successfully expanded to 90% height (fallback)');
        } else {
          debugPrint(
              '📋 SHEET: ❌ Controller still not attached after fallback delay');
          // Final fallback - force sheet expansion through state
          setState(() {
            _isSheetExpanded = true;
            _hasExpandedSheetForCurrentRoute = true;
          });
        }
      });
    }
  }

  // Method to minimize sheet to partial visibility after route completion
  void _minimizeSheetAfterRouteCompletion() {
    debugPrint(
        '📋 SHEET: Minimizing sheet to show route results after completion');

    // Only minimize if we're currently expanded and route calculation is complete
    if (!_isSheetExpanded) {
      debugPrint('📋 SHEET: Sheet not expanded, skipping minimization');
      return;
    }

    // Minimize to optimal size for result preview - higher than minimum to show results
    if (mounted && _draggableScrollableController.isAttached) {
      _draggableScrollableController.animateTo(
        0.50, // Increased to 50% to better show route results and station preview
        duration: const Duration(
            milliseconds: 600), // Slightly longer for smooth transition
        curve: Curves.easeOutQuart, // Smoother curve for result display
      );
      setState(() {
        _isSheetExpanded = true; // Keep as expanded but at result preview size
      });
      debugPrint(
          '📋 SHEET: ✅ Successfully minimized to 50% height for optimal result preview');
    } else {
      debugPrint('📋 SHEET: ❌ Controller not attached for minimization');
      // Fallback - try again after short delay
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted && _draggableScrollableController.isAttached) {
          _draggableScrollableController.animateTo(
            0.50,
            duration: const Duration(milliseconds: 600),
            curve: Curves.easeOutQuart,
          );
          setState(() {
            _isSheetExpanded = true;
          });
          debugPrint(
              '📋 SHEET: ✅ Successfully minimized to 50% height (fallback)');
        }
      });
    }
  }

  // Simplified method to handle route completion and camera positioning
  void _handleRouteCompletion() {
    if (_hasPositionedCameraForCurrentRoute) {
      debugPrint('📷 CAMERA: Already positioned for current route, skipping');
      return;
    }

    debugPrint('📷 CAMERA: Handling route completion and camera positioning');

    final routeState = ref.read(routeProvider);
    final routeBounds = ref.read(routeBoundsProvider);
    final polylines = ref.read(polylineProvider);

    debugPrint(
        '📷 CAMERA: Route state - hasRoute: ${routeState.hasRoute}, bounds: $routeBounds');
    debugPrint('📷 CAMERA: Polylines: ${polylines.length}');

    // Check if we have all the required data for camera positioning
    if (routeState.hasRoute && routeBounds != null && polylines.isNotEmpty) {
      debugPrint(
          '📷 CAMERA: ✅ All data available - executing camera positioning');
      debugPrint('📷 CAMERA: Route bounds: ${routeBounds.toString()}');
      debugPrint('📷 CAMERA: Polyline count: ${polylines.length}');

      // Position camera to show route
      _positionCameraToShowRoute(routeBounds);

      // Minimize sheet after camera positioning with a single delay
      Future.delayed(const Duration(milliseconds: 1200), () {
        if (mounted) {
          debugPrint('📋 SHEET: Camera positioning complete, minimizing sheet');
          _minimizeSheetAfterRouteCompletion();
        }
      });
    } else {
      debugPrint(
          '📷 CAMERA: ❌ Missing data - Route: ${routeState.hasRoute}, Bounds: ${routeBounds != null}, Polylines: ${polylines.length}');
      debugPrint(
          '📷 CAMERA: Route completion will be handled when all data is available');
    }
  }

  // Enhanced method to position camera with optimal route framing
  void _positionCameraToShowRoute(LatLngBounds bounds) {
    debugPrint(
        '📷 CAMERA: 🚀 EXECUTING OPTIMAL ROUTE FRAMING to show route bounds: $bounds');
    debugPrint(
        '📷 CAMERA: Southwest: ${bounds.southwest}, Northeast: ${bounds.northeast}');

    // Mark as positioned immediately to prevent duplicate calls
    setState(() {
      _hasPositionedCameraForCurrentRoute = true;
    });

    // Enhanced camera positioning with optimal bounds fitting
    if (_mapController != null) {
      debugPrint(
          '📷 CAMERA: Using direct map controller for optimal route framing');

      // Optimal padding for complete route visibility
      const double optimalPadding =
          80.0; // Balanced padding for complete route view

      try {
        // Create optimal camera update to frame the entire route
        final optimalCameraUpdate = CameraUpdate.newLatLngBounds(
          bounds,
          optimalPadding,
        );

        debugPrint(
            '📷 CAMERA: Applying optimal route framing with ${optimalPadding}px padding');

        // Animate to optimal route view with smooth transition
        _mapController!.animateCamera(optimalCameraUpdate).then((_) {
          debugPrint(
              '📷 CAMERA: ✅ Optimal route framing completed successfully');
          debugPrint(
              '📷 CAMERA: Complete route is now visible with ${optimalPadding}px padding');

          // Provide haptic feedback only (no visual notification)
          if (mounted) {
            HapticFeedback.mediumImpact();
          }
        }).catchError((error) {
          debugPrint('📷 CAMERA: ❌ Optimal camera animation failed: $error');
          _fallbackCameraPositioning(bounds);
        });
      } catch (e) {
        debugPrint('📷 CAMERA: ❌ Error creating optimal camera update: $e');
        _fallbackCameraPositioning(bounds);
      }
    } else {
      debugPrint(
          '📷 CAMERA: Map controller not available, using GoogleMapWidget fallback');
      _fallbackCameraPositioning(bounds);
    }
  }

  // Enhanced fallback camera positioning using GoogleMapWidget auto-fitting
  void _fallbackCameraPositioning(LatLngBounds bounds) {
    debugPrint(
        '📷 CAMERA: Using GoogleMapWidget fallback for optimal route framing');

    // Since we're using GoogleMapWidget, the fitBounds through the widget will handle camera positioning
    // The GoogleMapWidget will automatically zoom to fit the provided bounds with optimal padding
    if (mounted) {
      setState(() {
        debugPrint(
            '📷 CAMERA: ✅ OPTIMAL ROUTE FRAMING TRIGGERED! Rebuild initiated for complete route view');
        debugPrint(
            '📷 CAMERA: GoogleMapWidget will automatically fit bounds: ${bounds.toString()}');
      });

      // Verification after GoogleMapWidget processes the bounds
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          debugPrint(
              '📷 CAMERA: 🔍 Verification - GoogleMapWidget bounds fitting completed');

          final routeState = ref.read(routeProvider);
          final polylines = ref.read(polylineProvider);
          debugPrint(
              '📷 CAMERA: Final status: hasRoute=${routeState.hasRoute}, polylines=${polylines.length}');
          debugPrint(
              '📷 CAMERA: Map should now display complete route optimally framed with charging stations');

          // Provide haptic feedback only (no visual notification)
          if (mounted) {
            HapticFeedback.selectionClick();
          }
        }
      });
    }
  }

  // When the user taps on the map, collapse the bottom sheet (if not navigating)
  void _onMapTap(LatLng latLng) {
    // Always minimize the sheet when tapping on the map, unless in navigation mode
    if (!_isNavigating) {
      // Fold/minimize the sheet to its minimum size
      if (_draggableScrollableController.isAttached) {
        _draggableScrollableController.animateTo(
          0.1, // Minimum sheet size
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutCubic,
        );
      }

      setState(() {
        _isSheetExpanded = false;
      });

      // Unfocus any text field to dismiss keyboard
      FocusScope.of(context).unfocus();
    }
  }

  // Handler for GoogleMapWidget tap
  void _handleMapTap(LatLng position) {
    // Just delegate to the main map tap handler with the position
    _onMapTap(position);
  }

  // Handler for polyline taps (route selection)
  void _handlePolylineTap(String polylineId) {
    debugPrint('🗺️ TRIP: === POLYLINE TAP HANDLER ===');
    debugPrint('🗺️ TRIP: Polyline tapped - ID: $polylineId');

    // Check if this is a route alternative selection
    final routeState = ref.read(routeProvider);
    if (routeState.hasRouteAlternatives &&
        routeState.routeAlternatives != null) {
      final alternatives = routeState.routeAlternatives!.alternatives;
      final selectedAlternative = alternatives.firstWhere(
        (alt) => alt.id == polylineId,
        orElse: () => alternatives.first,
      );

      debugPrint(
          '🗺️ TRIP: Selecting route alternative: ${selectedAlternative.id}');
      debugPrint(
          '🗺️ TRIP: Route optimization: ${selectedAlternative.optimization.toString()}');
      debugPrint(
          '🗺️ TRIP: Route distance: ${selectedAlternative.route.distance}');
      debugPrint(
          '🗺️ TRIP: Route duration: ${selectedAlternative.route.duration}');

      // Select the route alternative
      ref.read(routeProvider.notifier).selectRouteAlternative(polylineId);

      // Show feedback to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '🛣️ Selected ${selectedAlternative.optimization.toString().split('.').last} route - ${selectedAlternative.route.distance} in ${selectedAlternative.route.duration}',
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );

      // Force UI update
      setState(() {
        // Trigger rebuild to update polyline styling
      });
    } else {
      debugPrint('🗺️ TRIP: Single route tapped - no alternatives available');
    }

    debugPrint('🗺️ TRIP: === END POLYLINE TAP HANDLER ===');
  }

  // Handler for station selection along route
  void _handleStationSelection(Map<String, dynamic> station) {
    debugPrint('🗺️ TRIP: === STATION SELECTION HANDLER ===');
    debugPrint('🗺️ TRIP: Station selected: ${station['name']}');
    debugPrint('🗺️ TRIP: Station ID: ${station['id']}');
    debugPrint('🗺️ TRIP: Station UID: ${station['uid']}');

    // Navigate to station details page
    if (station['uid'] != null && station['uid'].toString().isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => StationDetailsPage(
            uid: station['uid'].toString(),
          ),
        ),
      );

      // Show feedback
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('📍 Opening ${station['name']} details'),
          backgroundColor: Colors.blue,
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      debugPrint('🗺️ TRIP: ❌ Station missing UID - cannot navigate');

      // Show error to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              '❌ Unable to open ${station['name']} - missing station data'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }

    debugPrint('🗺️ TRIP: === END STATION SELECTION HANDLER ===');
  }

  @override
  Widget build(BuildContext context) {
    // Use the current theme from the context
    return Builder(
      builder: (context) => Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        // Main Stack to overlay Map, TopBar, and BottomSheet
        body: Stack(
          children: [
            // Use GoogleMapWidget instead of GoogleMap when useMapWidget is true
            useMapWidget
                ? Consumer(
                    builder: (context, ref, child) {
                      // GoogleMapWidget implementation - shows contextual route polylines
                      // Watch route state for polylines and bounds
                      final polylines = ref.watch(polylineProvider);
                      final routeBounds = ref.watch(routeBoundsProvider);
                      final routeState = ref.watch(routeProvider);
                      // Watch trip markers for location pins
                      final tripMarkers = ref.watch(tripMarkersSetProvider);
                      // Watch destination stations along the route
                      final destinationStations =
                          ref.watch(destinationStationsMapProvider);

                      // Enhanced monitoring for immediate route auto-zoom
                      // Trigger enhanced camera zoom immediately when polylines are generated
                      if (routeState.hasRoute &&
                          routeBounds != null &&
                          polylines.isNotEmpty &&
                          !_hasPositionedCameraForCurrentRoute) {
                        debugPrint(
                            '📷 CAMERA: Polylines generated! Triggering immediate enhanced camera zoom');
                        debugPrint(
                            '📷 CAMERA: Route has ${polylines.length} polylines, bounds: $routeBounds');
                        debugPrint(
                            '📷 CAMERA: Initiating auto-zoom to frame complete route optimally');

                        // Use post-frame callback to avoid calling setState during build
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          _handleRouteCompletion();
                        });
                      }

                      // Enhanced polylines debugging for GoogleMapWidget
                      debugPrint(
                          '🗺️ TRIP: Using GoogleMapWidget implementation - CONTEXTUAL polylines');
                      debugPrint(
                          '🗺️ TRIP: Polylines count: ${polylines.length}');
                      if (polylines.isNotEmpty) {
                        debugPrint(
                            '🗺️ TRIP: ✅ Passing ${polylines.length} route polylines to GoogleMapWidget');
                        for (final polyline in polylines) {
                          debugPrint(
                              '🗺️ TRIP: - ${polyline.polylineId.value}: ${polyline.points.length} points');
                        }
                      } else {
                        debugPrint(
                            '🗺️ TRIP: ❌ No route polylines available - clean map');
                        final routeState = ref.read(routeProvider);
                        debugPrint(
                            '🗺️ TRIP: Route state - hasRoute: ${routeState.hasRoute}, points: ${routeState.polylinePoints.length}');
                      }

                      if (kDebugMode) {
                        debugPrint('🗺️ TRIP: === MARKERS DEBUG ===');
                        debugPrint(
                            '🗺️ TRIP: Trip markers available: ${tripMarkers.length}');
                        debugPrint(
                            '🗺️ TRIP: Passing 0 additional markers (start/destination markers removed)');
                        debugPrint(
                            '🗺️ TRIP: Destination stations: ${destinationStations.length}');
                        debugPrint('🗺️ TRIP: === END MARKERS DEBUG ===');
                      }

                      return GoogleMapWidget(
                        onTap: _handleMapTap, // Use the handler method
                        // Add destination stations from route provider
                        stations: destinationStations,
                        polylines:
                            polylines, // CONTEXTUAL: Use polylines from route provider - shows routes when calculated
                        fitBounds: routeBounds,
                        // Remove start and destination markers - only show route polylines and charging stations
                        additionalMarkers: const <Marker>{},
                        // Add polyline tap handler for route selection
                        onPolylineTapped: _handlePolylineTap,
                        // Add station selection handler
                        onStationSelected: _handleStationSelection,
                      );
                    },
                  ) // GoogleMapWidget: Shows contextual route polylines + supports polyline tap events
                : Consumer(
                    builder: (context, ref, child) {
                      // Direct GoogleMap implementation - clean map with NO polylines
                      // Get current theme state
                      final themeNotifier =
                          ref.watch(themeNotifierProvider.notifier);
                      final isDarkMode = themeNotifier.isDarkMode;
                      final mapStyle = GoogleMapsStyles.getMapStyle(isDarkMode);

                      // Enhanced debugging for direct GoogleMap
                      debugPrint(
                          '🗺️ TRIP: Using direct GoogleMap implementation - CLEAN map with NO polylines');
                      debugPrint(
                          '🗺️ TRIP: Station markers count: ${_stationMarkers.length}');

                      return GoogleMap(
                        onMapCreated: _onMapCreated,
                        initialCameraPosition: _initialCameraPosition,
                        style: mapStyle, // Apply theme-based map style
                        markers: _stationMarkers,
                        polylines: const <Polyline>{}, // CLEAN: Explicitly NO polylines for clean map view
                        onTap: _onMapTap,
                        myLocationEnabled:
                            true, // Requires location permissions
                        myLocationButtonEnabled: true,
                      );
                    },
                  ), // Direct GoogleMap: Clean map with NO polylines, only station markers
            // Top Bar for start location and destination search fields
            SafeArea(child: _buildTopBar()),

            // Route Planning Loading Overlay - REMOVED
            // Loading indicators should only appear on station list page, not during location selection
            const SizedBox.shrink(),

            // Collapsible Bottom Sheet to show trip summary and station list or navigation info
            _buildBottomSheet(),

            // Removed the positioned buttons since they're now in the top bar
          ],
        ),
        // Removed the bottom navigation bar
      ),
    );
  }

  /// Enhanced top bar with modern design and animations
  Widget _buildTopBar() {
    return AnimatedBuilder(
      animation: _searchBarAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, -50 * (1 - _searchBarAnimation.value)),
          child: Opacity(
            opacity: _searchBarAnimation.value,
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppThemes.darkCard
                    : Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.black.withAlpha(60)
                        : Colors.black.withAlpha(26),
                    blurRadius: 8,
                    spreadRadius: 0,
                    offset: const Offset(0, 4),
                  ),
                ],
                border: Border.all(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppThemes.darkBorder
                      : Colors.grey.shade300,
                  width: 1.5,
                ),
              ),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  // Search fields container
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? AppThemes.darkCard
                          : Colors.white,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Start location field with animation
                        TweenAnimationBuilder<double>(
                          tween: Tween<double>(begin: 0.0, end: 1.0),
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOutCubic,
                          builder: (context, value, child) {
                            return Opacity(
                              opacity: value,
                              child: Transform.translate(
                                offset: Offset(0, (1 - value) * -10),
                                child: child,
                              ),
                            );
                          },
                          child: Row(
                            children: [
                              Container(
                                width: 36,
                                height: 36,
                                decoration: BoxDecoration(
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? AppThemes.secondaryDarkColor
                                          .withAlpha(128)
                                      : AppTheme.secondaryLightColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Center(
                                  child: Icon(
                                    Icons.my_location,
                                    color: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? Colors.lightBlueAccent
                                        : AppTheme.secondaryColor,
                                    size: 20,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () {
                                      // Show location picker for start location
                                      debugPrint(
                                          '🎯 LOCATION PICKER: Start location field tapped');
                                      HapticFeedback.selectionClick();
                                      _showLocationPicker(isDestination: false);
                                    },
                                    splashColor:
                                        AppTheme.secondaryColor.withAlpha(30),
                                    highlightColor:
                                        AppTheme.secondaryColor.withAlpha(20),
                                    borderRadius: BorderRadius.circular(12),
                                    // Increase the hit target area with padding
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8.0),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Start Location',
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                              color: Theme.of(context)
                                                          .brightness ==
                                                      Brightness.dark
                                                  ? AppThemes.darkTextSecondary
                                                  : AppTheme.textSecondaryColor,
                                            ),
                                          ),
                                          const SizedBox(height: 2),
                                          Text(
                                            _startLocationController.text,
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                              color: Theme.of(context)
                                                          .brightness ==
                                                      Brightness.dark
                                                  ? Colors.white
                                                  : AppTheme.textPrimaryColor,
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),

                        // Animated divider
                        TweenAnimationBuilder<double>(
                          tween: Tween<double>(begin: 0.0, end: 1.0),
                          duration: const Duration(milliseconds: 400),
                          curve: Curves.easeOutCubic,
                          builder: (context, value, child) {
                            return Container(
                              margin: const EdgeInsets.symmetric(vertical: 12),
                              width: MediaQuery.of(context).size.width * value,
                              height: 1,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.grey.withAlpha(40),
                                    Colors.grey.withAlpha(10),
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                              ),
                            );
                          },
                        ),

                        // Destination field with animation
                        TweenAnimationBuilder<double>(
                          tween: Tween<double>(begin: 0.0, end: 1.0),
                          duration: const Duration(milliseconds: 400),
                          curve: Curves.easeOutCubic,
                          builder: (context, value, child) {
                            return Opacity(
                              opacity: value,
                              child: Transform.translate(
                                offset: Offset(0, (1 - value) * 10),
                                child: child,
                              ),
                            );
                          },
                          child: Row(
                            children: [
                              Container(
                                width: 36,
                                height: 36,
                                decoration: BoxDecoration(
                                  color: Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? AppThemes.secondaryDarkColor
                                          .withAlpha(128)
                                      : AppTheme.secondaryLightColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Center(
                                  child: Icon(
                                    Icons.location_on,
                                    color: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? Colors.redAccent
                                        : Colors.red,
                                    size: 20,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () {
                                      // Show destination picker
                                      debugPrint(
                                          '🎯 LOCATION PICKER: Destination field tapped');
                                      HapticFeedback.selectionClick();
                                      _showLocationPicker(isDestination: true);
                                    },
                                    splashColor: Colors.red.withAlpha(30),
                                    highlightColor: Colors.red.withAlpha(20),
                                    borderRadius: BorderRadius.circular(12),
                                    // Increase the hit target area with padding
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8.0),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Destination',
                                            style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                              color: Theme.of(context)
                                                          .brightness ==
                                                      Brightness.dark
                                                  ? AppThemes.primaryLightColor
                                                  : AppTheme.textSecondaryColor,
                                            ),
                                          ),
                                          const SizedBox(height: 2),
                                          Text(
                                            _destinationController.text.isEmpty
                                                ? 'Enter destination'
                                                : _destinationController.text,
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                              color: _destinationController
                                                      .text.isEmpty
                                                  ? (Theme.of(context)
                                                              .brightness ==
                                                          Brightness.dark
                                                      ? AppThemes.primaryColor
                                                          .withAlpha(128)
                                                      : AppTheme.textLightColor)
                                                  : (Theme.of(context)
                                                              .brightness ==
                                                          Brightness.dark
                                                      ? Colors.white
                                                      : AppTheme
                                                          .textPrimaryColor),
                                            ),
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              if (_destinationController.text.isNotEmpty)
                                TweenAnimationBuilder<double>(
                                  tween: Tween<double>(begin: 0.0, end: 1.0),
                                  duration: const Duration(milliseconds: 200),
                                  curve: Curves.easeOut,
                                  builder: (context, value, child) {
                                    return Opacity(
                                      opacity: value,
                                      child: Transform.scale(
                                        scale: 0.8 + (0.2 * value),
                                        child: child,
                                      ),
                                    );
                                  },
                                  child: IconButton(
                                    icon: Icon(
                                      Icons.close_rounded,
                                      color: Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? AppThemes.darkTextSecondary
                                          : AppTheme.textSecondaryColor,
                                      size: 20,
                                    ),
                                    onPressed: () {
                                      HapticFeedback.selectionClick();
                                      setState(() {
                                        _destinationController.clear();
                                        _isSheetExpanded = false;
                                      });
                                    },
                                    padding: EdgeInsets.zero,
                                    constraints: const BoxConstraints(),
                                    splashRadius: 24,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Action buttons row (Clear routes + Swap)
                  if (_destinationController.text.isNotEmpty)
                    Positioned(
                      right: 16,
                      top: 0,
                      bottom: 0,
                      child: Consumer(
                        builder: (context, ref, child) {
                          final routeState = ref.watch(routeProvider);

                          return Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Clear routes button - only show when route is active
                              if (routeState.hasRoute) ...[
                                TweenAnimationBuilder<double>(
                                  tween: Tween<double>(begin: 0.0, end: 1.0),
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.elasticOut,
                                  builder: (context, value, child) {
                                    return Transform.scale(
                                      scale: value,
                                      child: child,
                                    );
                                  },
                                  child: Container(
                                    width: 36,
                                    height: 36,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? AppThemes.darkCard
                                          : Colors.grey.shade200,
                                      shape: BoxShape.circle,
                                      border: Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? Border.all(
                                              color: AppThemes.darkBorder)
                                          : null,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? Colors.black.withAlpha(60)
                                              : Colors.black.withAlpha(26),
                                          blurRadius: 8,
                                          spreadRadius: 0,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Material(
                                      color: Colors.transparent,
                                      shape: const CircleBorder(),
                                      clipBehavior: Clip.antiAlias,
                                      child: InkWell(
                                        onTap: _handleClearRoutes,
                                        splashColor:
                                            Theme.of(context).brightness ==
                                                    Brightness.dark
                                                ? AppThemes.darkTextSecondary
                                                    .withAlpha(50)
                                                : Colors.grey.withAlpha(50),
                                        child: Center(
                                          child: Icon(
                                            Icons.close_rounded,
                                            color: Theme.of(context)
                                                        .brightness ==
                                                    Brightness.dark
                                                ? AppThemes.darkTextSecondary
                                                : Colors.grey.shade600,
                                            size: 20,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                              ],

                              // Swap button
                              TweenAnimationBuilder<double>(
                                tween: Tween<double>(begin: 0.0, end: 1.0),
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.elasticOut,
                                builder: (context, value, child) {
                                  return Transform.scale(
                                    scale: value,
                                    child: child,
                                  );
                                },
                                child: Container(
                                  width: 36,
                                  height: 36,
                                  decoration: BoxDecoration(
                                    color: AppTheme.secondaryColor,
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: AppTheme.secondaryColor
                                            .withAlpha(60),
                                        blurRadius: 8,
                                        spreadRadius: 0,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    shape: const CircleBorder(),
                                    clipBehavior: Clip.antiAlias,
                                    child: InkWell(
                                      onTap: () {
                                        HapticFeedback.mediumImpact();
                                        setState(() {
                                          // Swap start location and destination with animation
                                          final temp =
                                              _startLocationController.text;
                                          _startLocationController.text =
                                              _destinationController.text;
                                          _destinationController.text = temp;

                                          // Swap the selected locations too
                                          final tempLocation =
                                              _selectedStartLocation;
                                          _selectedStartLocation =
                                              _selectedDestination;
                                          _selectedDestination = tempLocation;

                                          // Swap trip markers
                                          ref
                                              .read(
                                                  tripMarkersProvider.notifier)
                                              .swapLocations();

                                          // Recalculate route if needed
                                          if (_isSheetExpanded &&
                                              _selectedStartLocation
                                                      ?.coordinates !=
                                                  null &&
                                              _selectedDestination
                                                      ?.coordinates !=
                                                  null) {
                                            _calculateRoute();
                                          }
                                        });
                                      },
                                      splashColor: Colors.white.withAlpha(50),
                                      child: const Center(
                                        child: Icon(
                                          Icons.swap_vert_rounded,
                                          color: Colors.white,
                                          size: 20,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Clear routes functionality
  void _handleClearRoutes() {
    HapticFeedback.mediumImpact();

    // Clear the route from the route provider
    ref.read(routeProvider.notifier).clearRoute();

    // Clear trip markers
    ref.read(tripMarkersProvider.notifier).clearMarkers();

    // Reset UI state
    setState(() {
      _isSheetExpanded = false;
      _selectedStartLocation = null;
      _selectedDestination = null;
      _startLocationController.clear();
      _destinationController.clear();
    });

    // Provide user feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white, size: 20),
            SizedBox(width: 12),
            Text('Route cleared'),
          ],
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: const Color(0xFF4776E6),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  // Enhanced location picker with Google Places integration
  void _showLocationPicker({required bool isDestination}) {
    debugPrint('🎯 LOCATION PICKER: === SHOWING LOCATION PICKER ===');
    debugPrint('🎯 LOCATION PICKER: isDestination: $isDestination');
    debugPrint(
        '🎯 LOCATION PICKER: Start location before: $_selectedStartLocation');
    debugPrint('🎯 LOCATION PICKER: Destination before: $_selectedDestination');

    // Clear search state when opening picker
    ref.read(placeSearchProvider.notifier).clearSearch();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      transitionAnimationController: AnimationController(
        vsync: Navigator.of(context),
        duration: const Duration(milliseconds: 300),
      ),
      builder: (context) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? AppThemes.darkSurface
                : Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 5,
                margin: const EdgeInsets.symmetric(vertical: 10),
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[600]
                      : Colors.grey[400],
                  borderRadius: BorderRadius.circular(2.5),
                ),
              ),

              // Title
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  children: [
                    Icon(
                      isDestination ? Icons.location_on : Icons.my_location,
                      color:
                          isDestination ? Colors.red : AppTheme.secondaryColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      isDestination
                          ? 'Select Destination'
                          : 'Select Start Location',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),

              // Google Places Location Search Widget
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: LocationSearchWidget(
                    hintText: isDestination
                        ? 'Search destination'
                        : 'Search start location',
                    prefixIcon:
                        isDestination ? Icons.location_on : Icons.my_location,
                    iconColor:
                        isDestination ? Colors.red : AppTheme.secondaryColor,
                    showCurrentLocation: true,
                    onLocationSelected: (PlaceSuggestion suggestion) {
                      debugPrint(
                          '🎯 LOCATION WIDGET: Location selected callback triggered');
                      debugPrint(
                          '🎯 LOCATION WIDGET: Suggestion: ${suggestion.toString()}');
                      debugPrint(
                          '🎯 LOCATION WIDGET: Has coordinates: ${suggestion.coordinates != null}');
                      if (suggestion.coordinates != null) {
                        debugPrint(
                            '🎯 LOCATION WIDGET: Coordinates: ${suggestion.coordinates}');
                      }
                      _handleLocationSelected(suggestion, isDestination);
                      Navigator.pop(context);
                    },
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Handle location selection with coordinates
  void _handleLocationSelected(PlaceSuggestion suggestion, bool isDestination) {
    debugPrint('🎯 === LOCATION SELECTION STARTED ===');
    debugPrint('🎯 LOCATION SELECTED: ${suggestion.description}');
    debugPrint('📍 COORDINATES: ${suggestion.coordinates}');
    debugPrint('🎯 Is destination: $isDestination');
    debugPrint('🎯 Suggestion object: $suggestion');

    // Validate coordinates
    if (suggestion.coordinates == null) {
      debugPrint('❌ CRITICAL: Location selected but no coordinates available!');
      debugPrint('❌ PlaceId: ${suggestion.placeId}');
      debugPrint('❌ Description: ${suggestion.description}');

      // Show error to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Error: Location coordinates not available for ${suggestion.mainText}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
      return;
    }

    setState(() {
      if (isDestination) {
        _selectedDestination = suggestion;
        _destinationController.text = suggestion.mainText.isNotEmpty
            ? suggestion.mainText
            : suggestion.description;

        debugPrint('🎯 DESTINATION SET: ${_destinationController.text}');
        debugPrint('📍 DESTINATION COORDS: ${suggestion.coordinates}');

        // Update trip markers for destination
        ref
            .read(tripMarkersProvider.notifier)
            .updateDestinationLocation(suggestion);
      } else {
        _selectedStartLocation = suggestion;
        _startLocationController.text = suggestion.mainText.isNotEmpty
            ? suggestion.mainText
            : suggestion.description;

        debugPrint('🎯 START LOCATION SET: ${_startLocationController.text}');
        debugPrint('📍 START LOCATION COORDS: ${suggestion.coordinates}');

        // Update trip markers for start location
        ref.read(tripMarkersProvider.notifier).updateStartLocation(suggestion);
      }
    });

    // Enhanced debugging for route calculation trigger
    debugPrint('🔍 === ROUTE CALCULATION CHECK ===');
    debugPrint('🔍 Start location object: $_selectedStartLocation');
    debugPrint(
        '🔍 Start location coords: ${_selectedStartLocation?.coordinates}');
    debugPrint('🔍 Destination object: $_selectedDestination');
    debugPrint('🔍 Destination coords: ${_selectedDestination?.coordinates}');
    debugPrint(
        '🔍 Both locations available: ${_selectedStartLocation?.coordinates != null && _selectedDestination?.coordinates != null}');

    if (_selectedStartLocation?.coordinates != null &&
        _selectedDestination?.coordinates != null) {
      debugPrint(
          '✅ BOTH LOCATIONS AVAILABLE - TRIGGERING IMMEDIATE LOADING AND ROUTE CALCULATION');
      debugPrint('✅ Origin: ${_selectedStartLocation!.coordinates!}');
      debugPrint('✅ Destination: ${_selectedDestination!.coordinates!}');

      // CRITICAL FIX: IMMEDIATE LOADING INDICATORS - Show loading state INSTANTLY
      setState(() {
        _isSheetExpanded = true; // Expand sheet immediately
        _isLoadingStationsLocally = true; // Set loading state IMMEDIATELY
      });

      // Handle coordinated sheet expansion and camera positioning with immediate loading feedback
      debugPrint(
          '✅ IMMEDIATE EXPANSION: Triggering immediate sheet expansion with loading feedback');
      _handleRouteCalculationStart();

      debugPrint(
          '🔄 IMMEDIATE LOADING: Local loading state set to TRUE - UI will show loading indicators instantly');

      // Route completion is now handled directly in _calculateRoute() method

      // CRITICAL FIX: Clear all previous route data BEFORE starting new calculation
      debugPrint(
          '🗑️ CLEARING: Clearing all previous route data before new calculation');
      ref.read(routeProvider.notifier).clearRoute();
      ref.read(tripMarkersProvider.notifier).clearMarkers();

      // Reset flags for new route calculation
      setState(() {
        _hasExpandedSheetForCurrentRoute = false;
        _hasPositionedCameraForCurrentRoute = false;
      });

      debugPrint('🗑️ CLEARING: All previous data cleared successfully');

      // Add timeout mechanism to prevent infinite loading (fallback safety)
      Timer(const Duration(seconds: 20), () {
        if (mounted && _isLoadingStationsLocally) {
          debugPrint(
              '⏰ TIMEOUT: Clearing local loading state after 20 seconds');
          setState(() {
            _isLoadingStationsLocally = false;
          });
        }
      });

      // CRITICAL FIX: Force immediate route calculation with proper parameters
      Future.microtask(() async {
        debugPrint('🚀 IMMEDIATE ROUTE CALCULATION TRIGGERED WITH FRESH DATA');
        await _calculateRoute();
      });
    } else {
      debugPrint('⏳ WAITING FOR BOTH LOCATIONS TO BE SET');
      if (_selectedStartLocation?.coordinates == null) {
        debugPrint('❌ Missing start location coordinates');
      }
      if (_selectedDestination?.coordinates == null) {
        debugPrint('❌ Missing destination coordinates');
      }

      // CRITICAL FIX: Clear loading state AND all route data if we don't have both locations
      if (_isLoadingStationsLocally) {
        debugPrint(
            '🔄 CLEARING LOADING: Not both locations available - clearing all data');
        setState(() {
          _isLoadingStationsLocally = false;
        });

        // Clear route data when locations are incomplete
        ref.read(routeProvider.notifier).clearRoute();
      }
    }
    debugPrint('🔍 === END ROUTE CALCULATION CHECK ===');

    // Show success feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '${isDestination ? 'Destination' : 'Start location'} set: ${suggestion.mainText}',
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );

    debugPrint('🎯 === LOCATION SELECTION COMPLETED ===');
  }

  // Enhanced bottom sheet with modern design and animations
  Widget _buildBottomSheet() {
    // Always show the sheet when destination is set, but start minimized
    if (_destinationController.text.isEmpty && !_isNavigating) {
      return const SizedBox.shrink();
    }

    // If navigating, show a compact navigation bar at the bottom with enhanced design
    if (_isNavigating) {
      return Align(
        alignment: Alignment.bottomCenter,
        child: TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0.0, end: 1.0),
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeOutQuart,
          builder: (context, value, child) {
            return Transform.translate(
              offset: Offset(0, 100 * (1 - value)),
              child: Opacity(
                opacity: value,
                child: child,
              ),
            );
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? AppThemes.darkCard
                  : Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.black.withAlpha(60)
                      : Colors.black.withAlpha(20),
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, -2),
                ),
              ],
              border: Border.all(
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppThemes.primaryColor.withAlpha(77)
                    : Colors.transparent,
                width: 1.5,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? AppThemes.secondaryDarkColor.withAlpha(77)
                        : AppTheme.secondaryLightColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Icon(
                      Icons.navigation_rounded,
                      color: AppTheme.secondaryColor,
                      size: 22,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Navigating to',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? AppThemes.primaryLightColor
                              : AppTheme.textSecondaryColor,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        _destinationController.text,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : AppTheme.textPrimaryColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Material(
                  color: Colors.transparent,
                  shape: const CircleBorder(),
                  clipBehavior: Clip.antiAlias,
                  child: IconButton(
                    icon: const Icon(Icons.close_rounded,
                        color: AppTheme.errorColor),
                    onPressed: () {
                      // Cancel navigation with haptic feedback
                      HapticFeedback.mediumImpact();
                      setState(() {
                        _isNavigating = false;
                        _isSheetExpanded = true;
                      });
                    },
                    splashColor: AppTheme.errorColor.withAlpha(30),
                    highlightColor: AppTheme.errorColor.withAlpha(20),
                    tooltip: 'Cancel navigation',
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Enhanced bottom sheet with trip summary and station list
    return Align(
      alignment: Alignment.bottomCenter,
      child: NotificationListener<DraggableScrollableNotification>(
        onNotification: (notification) {
          setState(() {
            _isSheetExpanded = notification.extent > 0.15;
          });
          return true;
        },
        child: DraggableScrollableSheet(
          controller: _draggableScrollableController,
          initialChildSize: 0.15, // Start with minimum visible height
          minChildSize: 0.15, // Minimum to ensure always visible
          maxChildSize: 0.90, // Increased max for better loading visibility
          snap: true,
          snapSizes: const [
            0.15,
            0.50,
            0.90
          ], // Updated snap sizes for better workflow
          // Enhanced snap behavior for better physics feel
          builder: (BuildContext context, ScrollController scrollController) {
            return GestureDetector(
              // Make the entire sheet draggable
              onVerticalDragUpdate: (details) {
                // Manually control the sheet position based on drag
                if (_draggableScrollableController.isAttached) {
                  _draggableScrollableController.jumpTo(
                      _draggableScrollableController.size -
                          (details.delta.dy /
                              MediaQuery.of(context).size.height));
                }
              },
              onVerticalDragEnd: (details) {
                if (!_draggableScrollableController.isAttached) {
                  return;
                }

                final velocity = details.primaryVelocity ?? 0;
                final currentSize = _draggableScrollableController.size;

                if (velocity < -300) {
                  // Swipe up - expand to next level
                  if (currentSize < 0.3) {
                    _draggableScrollableController.animateTo(
                      0.5, // Mid sheet size
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeOutCubic,
                    );
                  } else {
                    _draggableScrollableController.animateTo(
                      0.85, // Max sheet size
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeOutCubic,
                    );
                  }
                } else if (velocity > 300) {
                  // Swipe down - collapse to previous level
                  if (currentSize > 0.7) {
                    _draggableScrollableController.animateTo(
                      0.5, // Mid sheet size
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeOutCubic,
                    );
                  } else {
                    _draggableScrollableController.animateTo(
                      0.15, // Min sheet size (always visible)
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeOutCubic,
                    );
                  }
                }
              },
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppThemes.darkSurface
                      : Colors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.black.withAlpha(60)
                          : Colors.black.withAlpha(20),
                      blurRadius: 10,
                      spreadRadius: 0,
                      offset: const Offset(0, -4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Handle indicator for dragging
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      child: Center(
                        child: Container(
                          width: 40,
                          height: 4,
                          decoration: BoxDecoration(
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? Colors.grey.shade600
                                    : Colors.grey.shade300,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                    ),

                    // Route alternatives selector (always visible when destination is set)
                    if (_destinationController.text.isNotEmpty)
                      const RouteAlternativesSelector(), // No extra padding - widget handles its own margins

                    // Charging stations header (professional, no animations)
                    if (_isSheetExpanded &&
                        _destinationController.text.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.fromLTRB(20, 0, 20, 16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Container(
                                  width: 36,
                                  height: 36,
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryLightColor,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Center(
                                    child: Icon(
                                      Icons.ev_station_rounded,
                                      color: AppTheme.primaryColor,
                                      size: 20,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                const Text(
                                  'Charging Stations',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textPrimaryColor,
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? AppThemes.primaryDarkColor.withAlpha(77)
                                    : AppTheme.primaryLightColor,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Consumer(
                                builder: (context, ref, child) {
                                  final destinationStations =
                                      ref.watch(destinationStationsProvider);
                                  final isLoadingStations = ref.watch(
                                      destinationStationsLoadingProvider);

                                  // CRITICAL FIX: Show minimal loading indicator to prevent overflow
                                  if (_isLoadingStationsLocally ||
                                      isLoadingStations) {
                                    debugPrint(
                                        '🔄 LOADING UI: Showing minimal loading indicator - Local: $_isLoadingStationsLocally, Provider: $isLoadingStations');
                                    return SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2.0,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                AppTheme.primaryColor),
                                      ),
                                    );
                                  }

                                  return Text(
                                    '${destinationStations.length} found',
                                    style: TextStyle(
                                      color: AppTheme.primaryColor,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 12,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),

                    // List of charging stations with enhanced design
                    Expanded(
                      child: Consumer(
                        builder: (context, ref, child) {
                          final destinationStations =
                              ref.watch(destinationStationsProvider);
                          final isLoadingStations =
                              ref.watch(destinationStationsLoadingProvider);

                          // Debug station list population
                          if (kDebugMode && destinationStations.isNotEmpty) {
                            debugPrint('📋 TRIP: === STATION LIST DEBUG ===');
                            debugPrint(
                                '📋 Populating station list with ${destinationStations.length} stations');
                            for (int i = 0;
                                i < destinationStations.length && i < 3;
                                i++) {
                              final station = destinationStations[i];
                              debugPrint(
                                  '📋 List Item ${i + 1}: ${station.name} at (${station.latitude}, ${station.longitude})');
                            }
                            debugPrint('📋 === END STATION LIST DEBUG ===');
                          }

                          // IMMEDIATE LOADING: Show loading UI when local loading state is true OR provider is loading
                          if (_isLoadingStationsLocally || isLoadingStations) {
                            debugPrint(
                                '🔄 LOADING UI: Showing loading - Local: $_isLoadingStationsLocally, Provider: $isLoadingStations');
                            return _buildStationLoadingUI(context);
                          }

                          // CRITICAL FIX: Enhanced loading state clearing logic
                          // Clear local loading state when provider loading is complete OR when we have station data
                          if (_isLoadingStationsLocally &&
                              (!isLoadingStations ||
                                  destinationStations.isNotEmpty)) {
                            debugPrint(
                                '✅ LOADING COMPLETE: Clearing local loading state - Provider loading: $isLoadingStations, Stations: ${destinationStations.length}');
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              if (mounted) {
                                setState(() {
                                  _isLoadingStationsLocally = false;
                                });
                                debugPrint(
                                    '✅ LOADING CLEARED: Local loading state successfully cleared');
                              }
                            });
                          }

                          // CRITICAL FIX: Also clear loading state when route calculation is complete AND trigger camera positioning
                          final routeState = ref.watch(routeProvider);
                          final polylines = ref.watch(polylineProvider);
                          final routeBounds = ref.watch(routeBoundsProvider);

                          if (_isLoadingStationsLocally &&
                              routeState.hasRoute &&
                              !routeState.isLoading &&
                              polylines.isNotEmpty) {
                            debugPrint(
                                '✅ ROUTE COMPLETE: Route calculation finished - triggering camera positioning and sheet management');
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              if (mounted) {
                                setState(() {
                                  _isLoadingStationsLocally = false;
                                });
                                debugPrint(
                                    '✅ ROUTE CLEARED: Local loading state cleared after route completion');

                                // Trigger route completion handling for camera positioning and sheet minimization
                                if (routeBounds != null &&
                                    !_hasPositionedCameraForCurrentRoute) {
                                  debugPrint(
                                      '📷 TRIGGER: Initiating camera positioning and sheet minimization workflow');
                                  _handleRouteCompletion();
                                }
                              }
                            });
                          }

                          if (destinationStations.isEmpty &&
                              !_isLoadingStationsLocally &&
                              !isLoadingStations) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.ev_station_outlined,
                                    size: 48,
                                    color: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? AppThemes.darkTextSecondary
                                        : AppTheme.textSecondaryColor,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No stations found along this route',
                                    style: TextStyle(
                                      color: Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? AppThemes.darkTextSecondary
                                          : AppTheme.textSecondaryColor,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Try a different route or check nearby areas',
                                    style: TextStyle(
                                      color: Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? AppThemes.darkTextSecondary
                                          : AppTheme.textSecondaryColor,
                                      fontSize: 14,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            );
                          }

                          return NotificationListener<ScrollNotification>(
                            onNotification: (notification) {
                              // Handle all scroll notifications to implement physics-based behavior
                              if (notification is ScrollEndNotification) {
                                // When user stops scrolling, check if there's empty space at the bottom
                                if (scrollController.hasClients) {
                                  final maxScroll =
                                      scrollController.position.maxScrollExtent;
                                  final currentScroll =
                                      scrollController.position.pixels;
                                  final viewportDimension = scrollController
                                      .position.viewportDimension;

                                  // If we're not at the top and there's empty space (content doesn't fill viewport)
                                  if (currentScroll > 0 &&
                                      maxScroll < viewportDimension) {
                                    // Snap to the bottom with physics-based animation
                                    scrollController.animateTo(
                                      0, // Scroll to top since content is shorter than viewport
                                      duration:
                                          const Duration(milliseconds: 300),
                                      curve: Curves.easeOutCubic,
                                    );
                                  } else if (currentScroll < maxScroll) {
                                    // If we're not at the bottom and user released, snap to show the last card
                                    final remainingScrollableDistance =
                                        maxScroll - currentScroll;

                                    // If the remaining distance is less than 20% of viewport, snap to bottom
                                    if (remainingScrollableDistance <
                                        viewportDimension * 0.2) {
                                      scrollController.animateTo(
                                        maxScroll,
                                        duration:
                                            const Duration(milliseconds: 300),
                                        curve: Curves.easeOutCubic,
                                      );
                                    }
                                  }
                                }
                              }
                              return false;
                            },
                            child: ListView.builder(
                              physics: const BouncingScrollPhysics(
                                  parent:
                                      AlwaysScrollableScrollPhysics()), // Enhanced physics for better scrolling feel
                              controller: scrollController,
                              // Remove bottom padding to eliminate empty space
                              padding: const EdgeInsets.only(top: 8, bottom: 0),
                              itemCount: destinationStations.length,
                              itemBuilder: (context, index) {
                                final destinationStation =
                                    destinationStations[index];
                                // Simple, professional appearance without zoom animations
                                return _buildDestinationStationCard(
                                    destinationStation, context);
                              },
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  // Enhanced station card for destination stations with rich JSON data
  Widget _buildDestinationStationCard(
      DestinationStation destinationStation, BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Calculate EVSE statistics
    final totalEvses = destinationStation.evses?.length ?? 0;
    final availableEvses = destinationStation.evses
            ?.where((evse) =>
                evse is Map<String, dynamic> && evse['status'] == 'AVAILABLE')
            .length ??
        0;
    final inoperativeEvses = destinationStation.evses
            ?.where((evse) =>
                evse is Map<String, dynamic> && evse['status'] == 'INOPERATIVE')
            .length ??
        0;
    final unknownEvses = destinationStation.evses
            ?.where((evse) =>
                evse is Map<String, dynamic> && evse['status'] == 'UNKNOWN')
            .length ??
        0;

    // Get connector information
    final connectorTypes = destinationStation.getConnectorTypes();
    final maxPower = destinationStation.getMaxPower();
    final availableConnectors = destinationStation.getAvailableConnectorCount();

    // Status color based on availability - normalize to match centralized design
    Color statusColor = Colors.red; // Default to red for closed/unknown
    String statusText = 'Closed'; // Default to closed for consistency
    IconData statusIcon = Icons.close_rounded;

    if (availableEvses > 0) {
      statusColor = Colors.green;
      statusText = 'Available';
      statusIcon = Icons.electric_bolt;
    } else if (inoperativeEvses > 0 && availableEvses == 0) {
      statusColor = Colors.red;
      statusText = 'Closed'; // Normalize "Inoperative" to "Closed"
      statusIcon = Icons.close_rounded;
    } else if (unknownEvses > 0 && availableEvses == 0) {
      // TRIP PAGE SPECIFIC: Treat UNKNOWN EVSE status as "Busy" (orange) for trip planning context
      statusColor = Colors.orange;
      statusText = 'Busy';
      statusIcon = Icons.access_time;
    } else if (totalEvses == 0) {
      // No EVSE data available - default to Closed
      statusColor = Colors.red;
      statusText = 'Closed';
      statusIcon = Icons.close_rounded;
    }

    return Container(
      margin: const EdgeInsets.symmetric(
          horizontal: 16, vertical: 4), // Reduced vertical margin
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(12), // Smaller radius
        boxShadow: [
          BoxShadow(
            color: Colors.black
                .withValues(alpha: isDarkMode ? 0.2 : 0.08), // Reduced shadow
            blurRadius: 6, // Reduced blur
            offset: const Offset(0, 1), // Smaller offset
          ),
        ],
        border: Border.all(
          color: isDarkMode ? const Color(0xFF333333) : const Color(0xFFE0E0E0),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _navigateToStationDetails(destinationStation, context),
          child: Padding(
            padding: const EdgeInsets.all(12), // Reduced padding
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with station name and status
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Station icon - COMPACT
                    Container(
                      width: 40, // Smaller icon container
                      height: 40,
                      decoration: BoxDecoration(
                        color: statusColor.withValues(alpha: 0.1),
                        borderRadius:
                            BorderRadius.circular(10), // Smaller radius
                      ),
                      child: Icon(
                        statusIcon,
                        color: statusColor,
                        size: 20, // Smaller icon
                      ),
                    ),
                    const SizedBox(width: 10), // Reduced spacing

                    // Station name and location
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            destinationStation.name,
                            style: TextStyle(
                              fontSize: 14, // Smaller text
                              fontWeight: FontWeight.w600,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2), // Reduced spacing
                          Row(
                            children: [
                              Icon(
                                Icons.location_on_outlined,
                                size: 14,
                                color: isDarkMode
                                    ? Colors.grey[400]
                                    : Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  destinationStation.getFormattedAddress(),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: isDarkMode
                                        ? Colors.grey[400]
                                        : Colors.grey[600],
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Distance and status badge
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: statusColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                                color: statusColor.withValues(alpha: 0.3)),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(statusIcon, size: 12, color: statusColor),
                              const SizedBox(width: 4),
                              Text(
                                statusText,
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                  color: statusColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 4),
                        if (destinationStation.distance != null)
                          Text(
                            '${destinationStation.distance!.toStringAsFixed(1)} km',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: isDarkMode
                                  ? Colors.grey[300]
                                  : Colors.grey[700],
                            ),
                          ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 10), // Reduced spacing

                // EVSE Status Overview - COMPACT
                Container(
                  padding: const EdgeInsets.all(8), // Reduced padding
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? const Color(0xFF2A2A2A)
                        : const Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.circular(8), // Smaller radius
                  ),
                  child: Column(
                    children: [
                      // EVSE counts
                      Row(
                        children: [
                          Icon(
                            Icons.ev_station,
                            size: 16,
                            color: isDarkMode
                                ? Colors.grey[300]
                                : Colors.grey[700],
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Connectors',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: isDarkMode
                                  ? Colors.grey[300]
                                  : Colors.grey[700],
                            ),
                          ),
                          const Spacer(),
                          Text(
                            '$totalEvses Total',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Availability display matching centralized design
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            size: 14,
                            color: isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            totalEvses > 0
                                ? '$availableEvses/$totalEvses Connectors Available'
                                : 'No connector data',
                            style: TextStyle(
                              fontSize: 12,
                              color: isDarkMode
                                  ? Colors.grey[400]
                                  : Colors.grey[600],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Status breakdown
                      Row(
                        children: [
                          if (availableEvses > 0) ...[
                            _buildStatusChip('Available', availableEvses,
                                Colors.green, isDarkMode),
                            const SizedBox(width: 8),
                          ],
                          if (inoperativeEvses > 0) ...[
                            _buildStatusChip('Closed', inoperativeEvses,
                                Colors.red, isDarkMode),
                            const SizedBox(width: 8),
                          ],
                          if (unknownEvses > 0) ...[
                            _buildStatusChip('Busy', unknownEvses,
                                Colors.orange, isDarkMode),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 12),

                // Connector and Power Information
                Row(
                  children: [
                    // Connector types
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.power,
                                size: 14,
                                color: isDarkMode
                                    ? Colors.grey[400]
                                    : Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Connector Type',
                                style: TextStyle(
                                  fontSize: 11,
                                  color: isDarkMode
                                      ? Colors.grey[400]
                                      : Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 2),
                          Text(
                            connectorTypes.isNotEmpty
                                ? connectorTypes.first
                                    .replaceAll('IEC_62196_T2_COMBO', 'CCS2')
                                : 'Unknown',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Max power
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.flash_on,
                                size: 14,
                                color: isDarkMode
                                    ? Colors.grey[400]
                                    : Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Max Power',
                                style: TextStyle(
                                  fontSize: 11,
                                  color: isDarkMode
                                      ? Colors.grey[400]
                                      : Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 2),
                          Text(
                            maxPower != null ? '${maxPower}kW' : 'Unknown',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Available connectors
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.check_circle_outline,
                              size: 14,
                              color: availableConnectors > 0
                                  ? Colors.green
                                  : Colors.grey,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Available',
                              style: TextStyle(
                                fontSize: 11,
                                color: isDarkMode
                                    ? Colors.grey[400]
                                    : Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Text(
                          '$availableConnectors',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: availableConnectors > 0
                                ? Colors.green
                                : Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build status chips
  Widget _buildStatusChip(
      String label, int count, Color color, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        '$count $label',
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  // Helper method to build enhanced station loading UI
  Widget _buildStationLoadingUI(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Simple loading indicator without animations
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: isDarkMode ? AppThemes.darkCard : Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.3),
                  blurRadius: 12,
                  spreadRadius: 2,
                  offset: const Offset(0, 4),
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: isDarkMode ? 0.3 : 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color: AppTheme.primaryColor.withValues(alpha: 0.2),
                width: 2,
              ),
            ),
            child: Center(
              child: SizedBox(
                width: 40,
                height: 40,
                child: CircularProgressIndicator(
                  strokeWidth: 4,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.primaryColor,
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 32),

          // Enhanced loading text with gradient effect
          ShaderMask(
            shaderCallback: (bounds) => LinearGradient(
              colors: [
                AppTheme.primaryColor,
                AppTheme.primaryColor.withValues(alpha: 0.7),
              ],
            ).createShader(bounds),
            child: Text(
              'Calculating optimal route...',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w700,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 12),

          // Enhanced subtitle text
          Text(
            'Finding charging stations and route alternatives\nalong your journey',
            style: TextStyle(
              fontSize: 15,
              color: isDarkMode
                  ? AppThemes.darkTextSecondary
                  : AppTheme.textSecondaryColor,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 24),

          // Enhanced animated progress indicator
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: 1.0),
            duration: const Duration(milliseconds: 2000),
            builder: (context, value, child) {
              return Column(
                children: [
                  // Progress dots
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(5, (index) {
                      final delay = index * 0.2;
                      final animationValue = (value - delay).clamp(0.0, 1.0);
                      final opacity =
                          (math.sin(animationValue * math.pi * 2) + 1) / 2;
                      final scale = 0.5 + (opacity * 0.5);

                      return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 3),
                        width: 10,
                        height: 10,
                        decoration: BoxDecoration(
                          color:
                              AppTheme.primaryColor.withValues(alpha: opacity),
                          shape: BoxShape.circle,
                        ),
                        transform: Matrix4.identity()..scale(scale),
                      );
                    }),
                  ),

                  const SizedBox(height: 16),

                  // Progress text
                  Text(
                    'This may take a few moments...',
                    style: TextStyle(
                      fontSize: 12,
                      color: isDarkMode
                          ? AppThemes.darkTextSecondary.withValues(alpha: 0.7)
                          : AppTheme.textSecondaryColor.withValues(alpha: 0.7),
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              );
            },
            onEnd: () {
              // Restart the animation
              if (mounted) {
                setState(() {
                  // Trigger rebuild to restart animation
                });
              }
            },
          ),
        ],
      ),
    );
  }

  // Enhanced navigation method with proper UID handling and loading indicator
  void _navigateToStationDetails(
      DestinationStation destinationStation, BuildContext context) {
    // Store context reference for async operations
    final navigatorState = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Show loading indicator immediately
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Center(
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF1E1E1E)
                  : Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 32,
                  height: 32,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Opening Station Details',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.white
                        : Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Loading ${destinationStation.name}...',
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey[400]
                        : Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );

    // Prioritize UID field, fallback to ID if UID is empty
    final stationUid = destinationStation.uid.isNotEmpty
        ? destinationStation.uid
        : destinationStation.id;

    debugPrint('🏪 NAVIGATION: === STATION DETAILS NAVIGATION ===');
    debugPrint('🏪 NAVIGATION: Station name: ${destinationStation.name}');
    debugPrint('🏪 NAVIGATION: Original UID: "${destinationStation.uid}"');
    debugPrint('🏪 NAVIGATION: Original ID: "${destinationStation.id}"');
    debugPrint('🏪 NAVIGATION: Selected UID for navigation: "$stationUid"');
    debugPrint(
        '🏪 NAVIGATION: Station coordinates: (${destinationStation.latitude}, ${destinationStation.longitude})');

    // Small delay to show loading indicator, then navigate
    Future.delayed(const Duration(milliseconds: 500), () {
      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Dismiss loading dialog
      navigatorState.pop();

      if (stationUid.isNotEmpty) {
        // Convert DestinationStation to Station for compatibility with StationDetailsPage
        final station = Station(
          id: destinationStation.id,
          name: destinationStation.name,
          address: destinationStation.getFormattedAddress(),
          city: destinationStation.city,
          state: destinationStation.state,
          images: [],
          evses: [], // Will be loaded by StationDetailsPage using UID
          latitude: destinationStation.latitude,
          longitude: destinationStation.longitude,
          distance: destinationStation.distance ?? 0.0,
          status: destinationStation.status,
          rating: 0.0,
          reviews: 0,
          connectors: [], // Will be loaded by StationDetailsPage using UID
          uid: stationUid, // Essential for station details loading
        );

        debugPrint(
            '🏪 NAVIGATION: Station object created with UID: ${station.uid}');
        debugPrint('🏪 NAVIGATION: Navigating to StationDetailsPage...');

        if (mounted) {
          navigatorState.push(
            MaterialPageRoute(
              builder: (context) => StationDetailsPage(
                uid:
                    stationUid, // Primary parameter for station details loading
                station: station, // Secondary parameter for initial display
              ),
            ),
          );
        }

        debugPrint('🏪 NAVIGATION: Navigation initiated successfully');
      } else {
        debugPrint(
            '❌ NAVIGATION: No valid UID available for station: ${destinationStation.name}');
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                  'Cannot open station details: Missing station identifier'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    });
  }
}
