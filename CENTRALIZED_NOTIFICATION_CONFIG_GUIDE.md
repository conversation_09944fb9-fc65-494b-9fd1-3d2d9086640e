# Centralized Notification Configuration Guide

## 📋 Overview

This guide explains the centralized notification configuration system implemented in the EcoPlug Flutter app. The system consolidates all notification-related settings, channels, topics, and preferences into a single, maintainable configuration.

## 🏗️ Architecture

### Core Components

1. **NotificationConfig** (`lib/config/notification_config.dart`) - Central configuration hub
2. **NotificationManager** (`lib/services/notification_manager.dart`) - High-level notification operations
3. **UnifiedNotificationService** - Manages FCM and local notifications
4. **Configuration Models** - Type-safe configuration structures

## 📁 File Structure

```
lib/
├── config/
│   └── notification_config.dart          # Central configuration
├── services/
│   ├── notification_manager.dart         # High-level manager
│   ├── unified_notification_service.dart # Unified service
│   ├── fcm_service.dart                  # FCM implementation
│   └── charging_notification_service.dart # Charging notifications
├── providers/
│   └── notification_provider.dart        # Riverpod state management
└── screens/settings/
    └── notification_settings_screen.dart # Settings UI
```

## 🔧 Configuration Structure

### 1. Notification Channels

Centralized channel definitions with consistent properties:

```dart
static const Map<String, NotificationChannelConfig> channels = {
  'charging_session': NotificationChannelConfig(
    id: 'charging_session',
    name: 'Charging Session',
    description: 'Real-time EV charging session notifications',
    importance: Importance.high,
    playSound: false,
    enableVibration: false,
    showBadge: true,
    enableLights: true,
    ledColor: AppColors.primaryGreen,
    groupId: 'ecoplug_charging',
  ),
  // ... more channels
};
```

### 2. FCM Topics

Centralized topic management with metadata:

```dart
static const Map<String, TopicConfig> topics = {
  'charging_updates': TopicConfig(
    name: 'charging_updates',
    displayName: 'Charging Updates',
    description: 'Real-time charging session updates',
    defaultSubscribed: true,
    userConfigurable: true,
  ),
  // ... more topics
};
```

### 3. Notification Types

Predefined notification types with templates:

```dart
static const Map<String, NotificationTypeConfig> notificationTypes = {
  'charging_complete': NotificationTypeConfig(
    type: 'charging_complete',
    title: 'Charging Complete',
    defaultBody: 'Your EV is fully charged and ready to go!',
    channel: 'charging_session',
    priority: NotificationPriority.high,
    persistent: false,
    showProgress: false,
  ),
  // ... more types
};
```

### 4. User Preferences

Configurable user preferences with relationships:

```dart
static const Map<String, NotificationPreference> defaultPreferences = {
  'charging_updates': NotificationPreference(
    key: 'charging_updates',
    displayName: 'Charging Updates',
    description: 'Get notified about charging session progress',
    defaultEnabled: true,
    category: PreferenceCategory.charging,
    relatedTopics: ['charging_updates'],
    relatedChannels: ['charging_session', 'fcm_messages'],
  ),
  // ... more preferences
};
```

## 🚀 Usage Examples

### 1. Using NotificationManager

```dart
// Initialize
final manager = NotificationManager();
await manager.initialize();

// Show notification by type
await manager.showNotificationByType(
  'charging_complete',
  data: {
    'charge_percentage': 100,
    'station_name': 'EcoPlug Station',
  },
);

// Update user preferences
await manager.updateUserPreferences({
  'charging_updates': true,
  'promotions': false,
});
```

### 2. Using Configuration Directly

```dart
// Get channel configuration
final channelConfig = NotificationConfig.getChannelConfig('charging_session');
final androidChannel = channelConfig?.toAndroidChannel();

// Get user preferences
final preferences = NotificationConfig.getUserConfigurablePreferences();

// Get topics for a preference
final topics = NotificationConfig.getTopicsForPreference('charging_updates');
```

### 3. Dynamic UI Generation

```dart
// Generate preference switches from configuration
...NotificationConfig.getUserConfigurablePreferences().map((pref) {
  return SwitchListTile(
    title: Text(pref.displayName),
    subtitle: Text(pref.description),
    value: userPreferences[pref.key] ?? pref.defaultEnabled,
    onChanged: (value) => updatePreference(pref.key, value),
  );
}),
```

## 🎯 Benefits

### 1. **Centralized Management**
- All notification settings in one place
- Easy to modify and maintain
- Consistent configuration across the app

### 2. **Type Safety**
- Strongly typed configuration models
- Compile-time error checking
- IntelliSense support

### 3. **Dynamic UI Generation**
- Settings screens generated from configuration
- No need to manually update UI when adding preferences
- Consistent user experience

### 4. **Easy Maintenance**
- Add new notification types without code changes
- Modify channel properties in one place
- Update topic relationships centrally

### 5. **Validation**
- Built-in configuration validation
- Runtime checks for missing configurations
- Debug-friendly error messages

## 🔄 Adding New Configurations

### 1. Adding a New Notification Channel

```dart
// In NotificationConfig.channels
'new_channel': NotificationChannelConfig(
  id: 'new_channel',
  name: 'New Channel',
  description: 'Description of the new channel',
  importance: Importance.defaultImportance,
  playSound: true,
  enableVibration: true,
  showBadge: true,
  enableLights: true,
  ledColor: AppColors.primaryBlue,
  groupId: 'ecoplug_new',
),
```

### 2. Adding a New FCM Topic

```dart
// In NotificationConfig.topics
'new_topic': TopicConfig(
  name: 'new_topic',
  displayName: 'New Topic',
  description: 'Description of the new topic',
  defaultSubscribed: false,
  userConfigurable: true,
),
```

### 3. Adding a New Notification Type

```dart
// In NotificationConfig.notificationTypes
'new_notification': NotificationTypeConfig(
  type: 'new_notification',
  title: 'New Notification',
  defaultBody: 'This is a new notification type',
  channel: 'new_channel',
  priority: NotificationPriority.normal,
  persistent: false,
  showProgress: false,
),
```

### 4. Adding a New User Preference

```dart
// In NotificationConfig.defaultPreferences
'new_preference': NotificationPreference(
  key: 'new_preference',
  displayName: 'New Preference',
  description: 'Description of the new preference',
  defaultEnabled: true,
  category: PreferenceCategory.system,
  relatedTopics: ['new_topic'],
  relatedChannels: ['new_channel'],
),
```

## 🛠️ Configuration Constants

### App Colors
```dart
class AppColors {
  static const Color primaryGreen = Color(0xFF4CAF50);
  static const Color primaryBlue = Color(0xFF2196F3);
  static const Color primaryOrange = Color(0xFFFF9800);
  // ... more colors
}
```

### Timing Constants
```dart
class NotificationTiming {
  static const int ledOnMs = 800;
  static const int ledOffMs = 1200;
  static const int chargingUpdateInterval = 30000;
  // ... more timing constants
}
```

### Notification IDs
```dart
class NotificationIds {
  static const int chargingSession = 1001;
  static const int stationAlert = 1002;
  static const int fcmMessage = 2000;
  // ... more IDs
}
```

## 🔍 Helper Methods

### Configuration Retrieval
```dart
// Get specific configurations
final channelConfig = NotificationConfig.getChannelConfig('channel_id');
final topicConfig = NotificationConfig.getTopicConfig('topic_name');
final preference = NotificationConfig.getPreference('preference_key');

// Get collections
final userPreferences = NotificationConfig.getUserConfigurablePreferences();
final defaultTopics = NotificationConfig.getDefaultTopics();
final relatedTopics = NotificationConfig.getTopicsForPreference('key');
```

### Validation
```dart
// Validate configuration
final manager = NotificationManager();
final isValid = manager.validateConfiguration();

// Get configuration summary
final summary = manager.getConfigurationSummary();
```

## 📊 Configuration Summary

The centralized system includes:

- **7 Notification Channels** (charging, FCM, stations, trips, wallet, promotions, system)
- **10 FCM Topics** (general, charging, stations, trips, promotions, wallet, system, locations)
- **8 Notification Types** (charging states, station alerts, payments, promotions)
- **6 User Preferences** (charging, stations, trips, wallet, promotions, system)
- **Consistent Color Scheme** (7 predefined colors)
- **Timing Constants** (LED, vibration, update intervals)
- **Unique Notification IDs** (preventing conflicts)

## 🎉 Conclusion

The centralized notification configuration system provides:

- ✅ **Single Source of Truth** for all notification settings
- ✅ **Type-Safe Configuration** with compile-time checking
- ✅ **Dynamic UI Generation** from configuration
- ✅ **Easy Maintenance** and extensibility
- ✅ **Consistent User Experience** across the app
- ✅ **Validation and Error Checking** for reliability

This system makes it easy to manage notifications at scale while maintaining consistency and reducing the chance of configuration errors.
