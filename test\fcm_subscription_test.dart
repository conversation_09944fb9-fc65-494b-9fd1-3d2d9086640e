import 'package:flutter_test/flutter_test.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ecoplug/services/auth_notification_service.dart';
import 'package:ecoplug/services/fcm_subscription_service.dart';

/// Comprehensive FCM Subscription Test
/// Tests both charging_id and test_1 FCM topic subscriptions
void main() {
  group('FCM Subscription Tests', () {
    late AuthNotificationService authService;
    late FCMSubscriptionService fcmSubscriptionService;

    setUpAll(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      
      // Initialize services
      authService = AuthNotificationService();
      fcmSubscriptionService = FCMSubscriptionService();
      
      print('🔔 ===== FCM SUBSCRIPTION TEST SETUP COMPLETE =====');
    });

    test('Test 1: Verify test_1 topic subscription during login', () async {
      print('\n🔔 ===== TEST 1: test_1 TOPIC SUBSCRIPTION =====');
      
      try {
        // Clear any existing subscription data
        await authService.clearTestTopicSubscriptionData();
        print('🔔 Cleared existing test_1 subscription data');
        
        // Simulate login success which should trigger test_1 subscription
        print('🔔 Simulating login success...');
        await authService.onLoginSuccess(
          userId: 'test_user_${DateTime.now().millisecondsSinceEpoch}',
          userName: 'Test User',
          userEmail: '<EMAIL>',
        );
        
        // Wait for subscription to complete
        await Future.delayed(const Duration(seconds: 3));
        
        // Check subscription status
        final status = await authService.getTestTopicSubscriptionStatus();
        print('🔔 Test_1 subscription status: $status');
        
        // Verify subscription
        expect(status['topic_name'], equals('test_1'));
        expect(status['is_subscribed'], isTrue, 
               reason: 'test_1 topic should be subscribed after login');
        
        if (status['error_occurred'] == true) {
          print('⚠️ Warning: Errors occurred during subscription');
          print('Original Error: ${status['original_error']}');
          if (status['retry_error'] != null) {
            print('Retry Error: ${status['retry_error']}');
          }
        }
        
        print('✅ TEST 1 PASSED: test_1 topic subscription successful');
        
      } catch (e) {
        print('❌ TEST 1 FAILED: $e');
        rethrow;
      }
    });

    test('Test 2: Verify charging_id topic subscription', () async {
      print('\n🔔 ===== TEST 2: CHARGING_ID TOPIC SUBSCRIPTION =====');
      
      try {
        // Initialize FCM subscription service
        await fcmSubscriptionService.initialize();
        print('🔔 FCM subscription service initialized');
        
        // Test charging session subscription
        const testChargingId = 'TEST_CHARGING_123';
        print('🔔 Testing charging subscription for ID: $testChargingId');
        
        final subscriptionResult = await fcmSubscriptionService.subscribeToChargingNotifications(testChargingId);
        print('🔔 Charging subscription result: $subscriptionResult');
        
        // Verify subscription result
        expect(subscriptionResult, isTrue, 
               reason: 'Charging topic subscription should succeed');
        
        print('✅ TEST 2 PASSED: charging_id topic subscription successful');
        
      } catch (e) {
        print('❌ TEST 2 FAILED: $e');
        rethrow;
      }
    });

    test('Test 3: Verify both subscriptions work together', () async {
      print('\n🔔 ===== TEST 3: COMBINED SUBSCRIPTION TEST =====');
      
      try {
        // Clear existing data
        await authService.clearTestTopicSubscriptionData();
        print('🔔 Cleared test_1 subscription data');
        
        // Step 1: Login (should subscribe to test_1)
        print('🔔 Step 1: Simulating login for combined test...');
        await authService.onLoginSuccess(
          userId: 'combined_test_user_${DateTime.now().millisecondsSinceEpoch}',
          userName: 'Combined Test User',
          userEmail: '<EMAIL>',
        );
        
        // Wait for login subscription
        await Future.delayed(const Duration(seconds: 2));
        
        // Step 2: Start charging session (should subscribe to charging topic)
        const testChargingId = 'COMBINED_TEST_456';
        print('🔔 Step 2: Starting charging session for ID: $testChargingId');
        
        final chargingSubscription = await fcmSubscriptionService.subscribeToChargingNotifications(testChargingId);
        
        // Wait for charging subscription
        await Future.delayed(const Duration(seconds: 2));
        
        // Step 3: Verify both subscriptions
        print('🔔 Step 3: Verifying both subscriptions...');
        
        // Check test_1 subscription
        final test1Status = await authService.getTestTopicSubscriptionStatus();
        print('🔔 Test_1 status: ${test1Status['is_subscribed']}');
        
        // Check charging subscription
        print('🔔 Charging subscription result: $chargingSubscription');
        
        // Verify both are successful
        expect(test1Status['is_subscribed'], isTrue, 
               reason: 'test_1 should be subscribed after login');
        expect(chargingSubscription, isTrue, 
               reason: 'charging topic should be subscribed');
        
        print('✅ TEST 3 PASSED: Both subscriptions work together successfully');
        
      } catch (e) {
        print('❌ TEST 3 FAILED: $e');
        rethrow;
      }
    });

    test('Test 4: Verify subscription error handling', () async {
      print('\n🔔 ===== TEST 4: ERROR HANDLING TEST =====');
      
      try {
        // Test manual subscription to verify error handling works
        print('🔔 Testing manual test_1 subscription...');
        await authService.testFCMTopicSubscription();
        
        // Check if error handling data is properly stored
        final status = await authService.getTestTopicSubscriptionStatus();
        print('🔔 Subscription status after manual test: $status');
        
        // Verify status structure (should have all tracking fields)
        expect(status.containsKey('topic_name'), isTrue);
        expect(status.containsKey('is_subscribed'), isTrue);
        expect(status.containsKey('subscription_time'), isTrue);
        expect(status.containsKey('fcm_token_available'), isTrue);
        expect(status.containsKey('error_occurred'), isTrue);
        
        print('✅ TEST 4 PASSED: Error handling and tracking working correctly');
        
      } catch (e) {
        print('❌ TEST 4 FAILED: $e');
        rethrow;
      }
    });

    test('Test 5: Verify subscription data persistence', () async {
      print('\n🔔 ===== TEST 5: DATA PERSISTENCE TEST =====');
      
      try {
        // Subscribe to test_1
        await authService.testFCMTopicSubscription();
        await Future.delayed(const Duration(seconds: 1));
        
        // Get initial status
        final initialStatus = await authService.getTestTopicSubscriptionStatus();
        print('🔔 Initial subscription status: ${initialStatus['is_subscribed']}');
        
        // Create new service instance to test persistence
        final newAuthService = AuthNotificationService();
        final persistedStatus = await newAuthService.getTestTopicSubscriptionStatus();
        print('🔔 Persisted subscription status: ${persistedStatus['is_subscribed']}');
        
        // Verify data persisted
        expect(persistedStatus['is_subscribed'], equals(initialStatus['is_subscribed']),
               reason: 'Subscription status should persist across service instances');
        
        print('✅ TEST 5 PASSED: Subscription data persistence working correctly');
        
      } catch (e) {
        print('❌ TEST 5 FAILED: $e');
        rethrow;
      }
    });

    tearDownAll(() async {
      // Clean up test data
      try {
        await authService.clearTestTopicSubscriptionData();
        print('🔔 Test cleanup completed');
      } catch (e) {
        print('⚠️ Warning: Test cleanup failed: $e');
      }
    });
  });
}

/// Helper function to run comprehensive FCM subscription test
Future<void> runComprehensiveFCMTest() async {
  print('🔔 ===== STARTING COMPREHENSIVE FCM SUBSCRIPTION TEST =====');
  print('🔔 Testing both charging_id and test_1 FCM topic subscriptions');
  print('🔔 Timestamp: ${DateTime.now().toIso8601String()}');
  
  try {
    // Initialize services
    final authService = AuthNotificationService();
    final fcmService = FCMSubscriptionService();
    
    await authService.initialize();
    await fcmService.initialize();
    
    print('🔔 Services initialized successfully');
    
    // Test 1: test_1 subscription via login
    print('\n🔔 === Testing test_1 subscription ===');
    await authService.onLoginSuccess(
      userId: 'cmd_test_${DateTime.now().millisecondsSinceEpoch}',
      userName: 'CMD Test User',
      userEmail: '<EMAIL>',
    );
    
    await Future.delayed(const Duration(seconds: 3));
    final test1Status = await authService.getTestTopicSubscriptionStatus();
    print('🔔 test_1 subscription result: ${test1Status['is_subscribed']}');
    
    // Test 2: charging_id subscription
    print('\n🔔 === Testing charging_id subscription ===');
    const chargingId = 'CMD_TEST_CHARGING_789';
    final chargingResult = await fcmService.subscribeToChargingNotifications(chargingId);
    print('🔔 charging_id subscription result: $chargingResult');
    
    // Summary
    print('\n🔔 ===== TEST SUMMARY =====');
    print('🔔 test_1 subscription: ${test1Status['is_subscribed'] == true ? "✅ SUCCESS" : "❌ FAILED"}');
    print('🔔 charging_id subscription: ${chargingResult ? "✅ SUCCESS" : "❌ FAILED"}');
    
    if (test1Status['is_subscribed'] == true && chargingResult) {
      print('🔔 🎉 ALL FCM SUBSCRIPTIONS SUCCESSFUL! 🎉');
    } else {
      print('🔔 ⚠️ Some subscriptions failed - check logs above');
    }
    
  } catch (e) {
    print('🔔 ❌ COMPREHENSIVE TEST FAILED: $e');
    rethrow;
  }
}
