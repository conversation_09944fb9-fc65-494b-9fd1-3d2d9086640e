# FCM Token Console Logging Guide

## Overview

This guide shows how to use the FCM Token Console Logger to debug FCM tokens in your EcoPlug app. **All tokens are logged to the debug console only** - no UI components are involved.

## Features

✅ **Debug Console Only** - No UI components, tokens only appear in debug console  
✅ **Debug Mode Only** - Only works when `kDebugMode` is true  
✅ **Multiple Token Sources** - Logs from FCMService, Firebase Messaging, and test generation  
✅ **Easy Integration** - Simple static methods and extension methods  
✅ **Comprehensive Logging** - Includes token length, preview, timestamps, and status  

## Quick Start

### 1. Import the Logger

```dart
import 'package:ecoplug/debug/fcm_token_console_logger.dart';
```

### 2. Basic Usage

```dart
// Log current FCM token
await FCMTokenConsoleLogger.logCurrentToken();

// Generate and log new token
await FCMTokenConsoleLogger.generateAndLogToken();

// Quick one-line log
await FCMTokenConsoleLogger.quickLog();
```

### 3. Check Debug Console

Look for output starting with `🔥` in your IDE debug console:

```
🔥 ===== FCM TOKEN DEBUG CONSOLE =====
🔥 EXISTING FCM TOKEN (from FCMService):
🔥 Length: 152 characters
🔥 Token: dGhpcyBpcyBhIGZha2UgdG9rZW4gZm9yIGV4YW1wbGU...
🔥 Preview: dGhpcyBpcyBhIGZha2UgdG9rZW4gZm9yIGV4YW1wbGU...
🔥 =====================================
```

## Available Methods

### Core Logging Methods

| Method | Description |
|--------|-------------|
| `logCurrentToken()` | Log existing FCM token from FCMService |
| `generateAndLogToken()` | Generate new token and log it |
| `logAllTokens()` | Log all available tokens (existing + new + direct) |
| `quickLog()` | One-line token log |
| `logTokenWithTimestamp()` | Log token with current timestamp |
| `logFCMStatus()` | Log FCM service configuration and status |

### Special Methods

| Method | Description |
|--------|-------------|
| `logTokenRefresh(newToken)` | Log token refresh events |

### Extension Methods

```dart
// Use from any class
await this.logFCMToken();
await this.quickFCMLog();
```

## Integration Examples

### 1. App Initialization

```dart
// In main.dart or app startup
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp();
  
  // Log FCM status and token (debug only)
  await FCMTokenConsoleLogger.logFCMStatus();
  await FCMTokenConsoleLogger.logCurrentToken();
  
  runApp(MyApp());
}
```

### 2. Authentication Flow

```dart
class AuthService {
  Future<void> onLoginSuccess(String userId) async {
    // Your login logic...
    
    // Log FCM token for debugging (debug only)
    await FCMTokenConsoleLogger.logTokenWithTimestamp();
    
    // Continue with login flow...
  }
}
```

### 3. Notification Settings Screen

```dart
class NotificationSettingsScreen extends StatefulWidget {
  @override
  void initState() {
    super.initState();
    
    // Log current FCM token when settings screen loads
    _logFCMTokenForDebugging();
  }
  
  Future<void> _logFCMTokenForDebugging() async {
    await FCMTokenConsoleLogger.logCurrentToken();
  }
}
```

### 4. FCM Service Integration

```dart
class FCMService {
  Future<void> onTokenRefresh(String newToken) async {
    // Log token refresh event
    FCMTokenConsoleLogger.logTokenRefresh(newToken);
    
    // Your existing token refresh logic...
  }
}
```

### 5. Debug Button (Optional)

If you want a debug button that triggers console logging:

```dart
// Only shown in debug mode
if (kDebugMode)
  ElevatedButton(
    onPressed: () async {
      await FCMTokenConsoleLogger.logAllTokens();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('FCM tokens logged to console')),
      );
    },
    child: Text('Log FCM Tokens to Console'),
  ),
```

## Console Output Examples

### Current Token Log
```
🔥 ===== FCM TOKEN DEBUG CONSOLE =====
🔥 EXISTING FCM TOKEN (from FCMService):
🔥 Length: 152 characters
🔥 Token: dGhpcyBpcyBhIGZha2UgdG9rZW4...
🔥 Preview: dGhpcyBpcyBhIGZha2UgdG9rZW4gZm9yIGV4YW1wbGU...
🔥 =====================================
```

### New Token Generation
```
🔥 ===== GENERATING NEW FCM TOKEN =====
🔥 NEW FCM TOKEN GENERATED:
🔥 Length: 152 characters
🔥 Token: bmV3IGZha2UgdG9rZW4gZm9yIGV4YW1wbGU...
🔥 Preview: bmV3IGZha2UgdG9rZW4gZm9yIGV4YW1wbGU...
🔥 Generation Time: 245ms
🔥 ====================================
```

### FCM Status Log
```
🔥 ===== FCM SERVICE STATUS =====
🔥 FCM Supported: true
🔥 Authorization Status: AuthorizationStatus.authorized
🔥 Alert Setting: AppleNotificationSetting.enabled
🔥 Badge Setting: AppleNotificationSetting.enabled
🔥 Sound Setting: AppleNotificationSetting.enabled
🔥 Token Available: true
🔥 Token Length: 152
🔥 ==============================
```

### Quick Log
```
🔥 FCM TOKEN: dGhpcyBpcyBhIGZha2UgdG9rZW4gZm9yIGV4YW1wbGU...
```

### Token Refresh Log
```
🔥 ===== FCM TOKEN REFRESHED =====
🔥 New Token: cmVmcmVzaGVkIGZha2UgdG9rZW4...
🔥 Length: 152 characters
🔥 Timestamp: 2024-12-19T10:30:45.123Z
🔥 ===============================
```

## Best Practices

### 1. **Debug Mode Only**
- All logging automatically checks `kDebugMode`
- Safe to include in production code
- No performance impact in release builds

### 2. **Strategic Placement**
- Log tokens during app initialization
- Log after authentication events
- Log during notification setup
- Log on token refresh events

### 3. **Console Monitoring**
- Watch for `🔥` prefixed lines in debug console
- Use IDE search/filter for "FCM TOKEN"
- Monitor during development and testing

### 4. **Backend Team Sharing**
- Copy tokens from console output
- Share with backend team for testing
- Use for Firebase Console testing

## Troubleshooting

### No Console Output
- Ensure you're running in debug mode (`kDebugMode = true`)
- Check that Firebase is properly initialized
- Verify FCM permissions are granted

### Token is Null
- Check Firebase configuration
- Verify internet connection
- Ensure FCM service is initialized

### Permission Issues
- Check notification permissions
- Verify Firebase project configuration
- Test on physical device (not simulator)

## Security Notes

- ✅ **Debug Only**: Only works in debug builds
- ✅ **Console Only**: No UI exposure of tokens
- ✅ **No Storage**: Tokens are not stored locally
- ✅ **Production Safe**: Automatically disabled in release builds

## Integration Checklist

- [ ] Import `FCMTokenConsoleLogger`
- [ ] Add logging calls to key app events
- [ ] Test in debug mode
- [ ] Verify console output
- [ ] Share tokens with backend team
- [ ] Test notifications with generated tokens

This console logging approach provides secure, debug-only access to FCM tokens without any UI components or security concerns.
