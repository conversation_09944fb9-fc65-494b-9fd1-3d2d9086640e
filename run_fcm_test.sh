#!/bin/bash

echo "🔔 ===== FCM SUBSCRIPTION TEST RUNNER ====="
echo "🔔 Testing both charging_id and test_1 FCM subscriptions"
echo "🔔 =========================================="
echo

echo "🔔 Step 1: Running Dart test script..."
dart test_fcm_subscriptions.dart

echo
echo "🔔 Step 2: Running Flutter test (if available)..."
flutter test test/fcm_subscription_test.dart --verbose

echo
echo "🔔 Step 3: Checking implementation files..."
if [ -f "lib/services/auth_notification_service.dart" ]; then
    echo "✅ auth_notification_service.dart found"
else
    echo "❌ auth_notification_service.dart missing"
fi

if [ -f "lib/services/fcm_subscription_service.dart" ]; then
    echo "✅ fcm_subscription_service.dart found"
else
    echo "❌ fcm_subscription_service.dart missing"
fi

if [ -f "lib/debug/fcm_test_topic_subscription_test.dart" ]; then
    echo "✅ fcm_test_topic_subscription_test.dart found"
else
    echo "❌ fcm_test_topic_subscription_test.dart missing"
fi

echo
echo "🔔 ===== TEST SUMMARY ====="
echo "🔔 1. test_1 topic subscription: Implemented in auth_notification_service.dart"
echo "🔔    - Triggers automatically on login success"
echo "🔔    - Uses FirebaseMessaging.instance.subscribeToTopic('test_1')"
echo "🔔    - Includes comprehensive debugging and error handling"
echo "🔔"
echo "🔔 2. charging_id topic subscription: Implemented in fcm_subscription_service.dart"
echo "🔔    - Triggers when charging session starts"
echo "🔔    - Uses format: Charging_[charging_id]"
echo "🔔    - Includes retry logic and status tracking"
echo "🔔"
echo "🔔 3. Test widget: Available at lib/debug/fcm_test_topic_subscription_test.dart"
echo "🔔    - Manual testing interface"
echo "🔔    - Comprehensive status display"
echo "🔔    - Clear data functionality"
echo "🔔"
echo "🔔 ===== NEXT STEPS ====="
echo "🔔 1. Run your Flutter app"
echo "🔔 2. Log in to trigger test_1 subscription"
echo "🔔 3. Start charging to trigger charging_id subscription"
echo "🔔 4. Check debug console for subscription logs (look for 🔔 emoji)"
echo "🔔 5. Use the test widget for manual verification"
echo "🔔"
echo "🔔 Both FCM subscriptions should work correctly! 🎉"
echo
