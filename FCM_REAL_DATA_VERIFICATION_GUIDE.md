# FCM Real Data Verification Guide

## 🚨 **Problem Statement**
FCM notifications are showing default/fallback values (like "30% SOC") instead of actual real-time charging data from the server.

## 🔧 **Debugging Tools Implemented**

### **1. FCM Debug Service (`lib/services/fcm_debug_service.dart`)**
- Captures all FCM messages with detailed logging
- Validates charging data fields
- Tracks subscription status
- Generates comprehensive debug reports

### **2. FCM Debug Widget (`lib/debug/fcm_debug_widget.dart`)**
- Interactive UI for testing FCM functionality
- Topic subscription testing
- Message history viewing
- Debug report generation

### **3. Enhanced FCM Service Logging**
- Added comprehensive message validation in `lib/services/fcm_service.dart`
- Real-time data analysis and validation
- Default value detection

### **4. Enhanced Android FCM Service**
- Added charging data validation in `EcoPlugFirebaseMessagingService.kt`
- Detailed logging of all FCM message fields
- Real vs default value detection

## 🔍 **Verification Steps**

### **Step 1: Verify FCM Subscription**
```dart
// Add to your app for testing
import 'package:ecoplug/debug/fcm_debug_widget.dart';

// Navigate to FCM Debug Widget
Navigator.push(context, MaterialPageRoute(
  builder: (context) => const FCMDebugWidget(),
));
```

**Test Actions:**
1. Enter charging ID (e.g., "TRANSACTION_123")
2. Click "Test Charging Subscription"
3. Verify subscription to topic "Charging_TRANSACTION_123"
4. Check logs for subscription success

### **Step 2: Monitor FCM Messages**
**Look for these debug logs:**

```
🔍 ===== FCM MESSAGE DATA VALIDATION =====
🔍 Charging Data Fields:
🔍   ✅ soc: 45
🔍   ✅ power: 7.2 kW
🔍   ✅ energy: 12.5 kWh
🔍   ❌ cost: NOT_FOUND
✅ Real SOC data detected: 45
```

### **Step 3: Check for Default Values**
**Warning signs of default/fallback data:**
```
⚠️ WARNING: SOC appears to be default/fallback value: 30
❌ CRITICAL: No SOC data found in FCM message
🔍   ❌ soc: NOT_FOUND
```

### **Step 4: Validate Topic Format**
**Correct topic format:** `Charging_TRANSACTION_ID`
```
🔍 Message from topic: /topics/Charging_TRANSACTION_123
✅ Message from charging topic
```

## 📊 **Expected FCM Message Structure**

### **Correct FCM Message Format:**
```json
{
  "data": {
    "type": "charging",
    "soc": "45",
    "power": "7.2 kW",
    "energy": "12.5 kWh",
    "cost": "₹125.50",
    "timer": "01:45:30",
    "transaction_id": "TRANSACTION_123"
  },
  "notification": {
    "title": "Charging Update",
    "body": "Battery: 45% • Power: 7.2 kW"
  }
}
```

### **Problem Indicators:**
```json
{
  "data": {
    "type": "charging",
    "soc": "30",  // ❌ Default value
    "power": "",  // ❌ Empty
    "energy": "0.00 kWh"  // ❌ Default
  }
}
```

## 🛠 **Troubleshooting Guide**

### **Issue 1: No FCM Messages Received**
**Symptoms:**
- No debug logs showing FCM messages
- Debug widget shows "No messages received"

**Solutions:**
1. Verify FCM token generation
2. Check topic subscription format
3. Confirm backend is sending to correct topic
4. Test with Firebase Console

### **Issue 2: Default Values in Notifications**
**Symptoms:**
- Notifications show "30% SOC" or "0.0 kW"
- Debug logs show default/fallback values

**Solutions:**
1. Check backend FCM message payload
2. Verify server is sending real charging data
3. Confirm topic subscription matches server topic
4. Check data field mapping

### **Issue 3: Missing Charging Data Fields**
**Symptoms:**
- Debug logs show "NOT_FOUND" for data fields
- Incomplete notification content

**Solutions:**
1. Verify backend includes all required fields
2. Check field name mapping (soc vs charge_percentage)
3. Confirm data types (string vs number)

## 🧪 **Testing Commands**

### **1. Test FCM Token Generation**
```bash
# Check Android logs for FCM token
adb logcat | grep "FCM Token"
```

### **2. Test Topic Subscription**
```bash
# Check logs for subscription success
adb logcat | grep "subscribeToTopic"
```

### **3. Monitor FCM Messages**
```bash
# Monitor all FCM-related logs
adb logcat | grep -E "(FCM|Firebase|EcoPlugFirebase)"
```

### **4. Test with Firebase Console**
1. Go to Firebase Console → Cloud Messaging
2. Send test message to topic: `Charging_TRANSACTION_123`
3. Include data payload with charging fields
4. Monitor app logs for message reception

## 📋 **Verification Checklist**

### **FCM Setup Verification:**
- [ ] FCM token generated successfully
- [ ] Topic subscription working (format: `Charging_ID`)
- [ ] App receiving FCM messages
- [ ] Debug logs showing message details

### **Data Validation:**
- [ ] Real SOC values (not 30, 0, or empty)
- [ ] Real power values (not 0.0 kW or empty)
- [ ] Real energy values (not 0.00 kWh)
- [ ] Transaction ID present in messages
- [ ] All required fields populated

### **Notification Display:**
- [ ] Notifications show real charging data
- [ ] No default/fallback values displayed
- [ ] Comprehensive data in notification body
- [ ] Proper notification formatting

## 🔧 **Backend Requirements**

### **FCM Message Format Requirements:**
```json
{
  "to": "/topics/Charging_TRANSACTION_ID",
  "data": {
    "type": "charging",
    "soc": "REAL_BATTERY_PERCENTAGE",
    "power": "REAL_POWER_VALUE",
    "energy": "REAL_ENERGY_VALUE",
    "cost": "REAL_COST_VALUE",
    "timer": "REAL_TIMER_VALUE",
    "transaction_id": "ACTUAL_TRANSACTION_ID"
  },
  "notification": {
    "title": "Charging Update",
    "body": "Battery: {soc}% • Power: {power}"
  }
}
```

### **Critical Backend Checks:**
1. **Real Data**: Ensure server sends actual charging values, not defaults
2. **Topic Format**: Use exact format `Charging_TRANSACTION_ID`
3. **Field Names**: Use consistent field names (soc, power, energy, etc.)
4. **Data Types**: Send as strings for compatibility
5. **Frequency**: Send updates when data actually changes

## 📞 **Next Steps**

1. **Implement Debug Tools**: Add FCM debug widget to your app
2. **Monitor Logs**: Check debug logs for FCM message validation
3. **Test Subscription**: Verify topic subscription is working
4. **Validate Data**: Confirm real charging data in FCM messages
5. **Backend Coordination**: Work with backend team to ensure proper FCM payload

The debug tools will help identify exactly where the issue lies - whether it's subscription, message format, or data content.
