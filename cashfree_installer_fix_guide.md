# Cashfree "NOT_A<PERSON><PERSON><PERSON><PERSON> is not a trusted source" Fix Guide (PRODUCTION Environment)

## 🔍 Problem Analysis

### Error Details:
```
❌ CASHFREE: Error Message: NOT_AVAILAB<PERSON> is not a trusted source. App should be installed from play store or another whitelisted app store.
Error Code: installer_package_not_approved
```

### Root Cause:
- **Environment**: Using `CFEnvironment.PRODUCTION` (as required)
- **Installation Source**: App installed via debug/development (not Play Store)
- **Security Restriction**: Cashfree production environment only allows apps from trusted sources

## ✅ Solutions for PRODUCTION Environment

### Solution 1: Build Release APK (Recommended)
The most effective solution is to build and install a release APK:

```bash
# Build release APK
flutter build apk --release

# Install the release APK
adb install build/app/outputs/flutter-apk/app-release.apk
```

**Why this works:**
- Release builds have proper signing
- Cashfree recognizes them as "trusted"
- No installer source restrictions

### Solution 2: Build Android App Bundle (AAB)
For even better compatibility:

```bash
# Build AAB
flutter build appbundle --release

# Install using bundletool
bundletool build-apks --bundle=build/app/outputs/bundle/release/app-release.aab --output=app.apks
bundletool install-apks --apks=app.apks
```

### Solution 3: Configure Proper App Signing
Ensure your debug builds are properly signed:

1. **Create/Update `android/app/build.gradle`:**
```gradle
android {
    signingConfigs {
        debug {
            keyAlias 'androiddebugkey'
            keyPassword 'android'
            storeFile file('debug.keystore')
            storePassword 'android'
        }
        release {
            // Your release signing config
        }
    }
}
```

2. **Generate debug keystore if needed:**
```bash
keytool -genkey -v -keystore android/app/debug.keystore -alias androiddebugkey -keyalg RSA -keysize 2048 -validity 10000
```

### Solution 4: Use Profile Build
Profile builds are closer to release builds:

```bash
# Build profile APK
flutter build apk --profile

# Install profile APK
adb install build/app/outputs/flutter-apk/app-profile.apk
```

## 🧪 Testing the Fix

### Expected Debug Output (PRODUCTION):
```
🔔 CASHFREE: Auto-detected environment: PRODUCTION
🔔 CASHFREE: Environment: CFEnvironment.PRODUCTION
⚠️ CASHFREE: WARNING - Using PRODUCTION environment in debug mode
⚠️ CASHFREE: This may cause installer source errors
⚠️ CASHFREE: Consider building release APK for testing
```

### Test Steps:
1. **Build release APK** (recommended solution)
2. **Install release APK on device**
3. **Open the app**
4. **Go to Wallet screen**
5. **Tap "Add Money"**
6. **Select "Cashfree"**
7. **Enter amount (e.g., ₹1)**
8. **Tap "Pay"**

### Expected Result:
- ✅ No installer error
- ✅ Cashfree payment screen opens in PRODUCTION mode
- ✅ Real payment processing (be careful with amounts!)

## 🚀 Production Deployment

### For Production Release:
1. **Set `forceCashfreeSandbox = false`**
2. **Build release APK/AAB**
3. **Upload to Play Store**
4. **Install from Play Store**
5. **App will automatically use PRODUCTION environment**

### For Development/Testing:
1. **Keep `forceCashfreeSandbox = true`**
2. **All payments will use SANDBOX environment**
3. **No installer restrictions**

## 📋 Backend Considerations

### Server Environment Compatibility:
- **Sandbox Orders**: Work with both sandbox and production Cashfree accounts
- **Production Orders**: Only work with production Cashfree accounts
- **Testing**: Use sandbox orders for development testing

### API Endpoint:
The existing endpoint `https://api2.eeil.online/api/v1/user/payment/initiate-cashfree` works correctly and returns valid order data.

## 🔍 Troubleshooting

### If Error Still Occurs:
1. **Check Configuration**: Ensure `forceCashfreeSandbox = true`
2. **Clean Build**: Run `flutter clean && flutter pub get`
3. **Restart App**: Completely close and restart the app
4. **Check Logs**: Look for environment detection messages

### Debug Messages to Look For:
```
🔔 CASHFREE: 🛠️ FORCED SANDBOX MODE - Using SANDBOX environment
🔔 CASHFREE: Environment: CFEnvironment.SANDBOX
```

### If Using Production Environment:
```
🔔 CASHFREE: 🚀 RELEASE MODE - Auto-detected environment: PRODUCTION
```

## 📱 Device Compatibility

### Sandbox Mode Benefits:
- ✅ Works on all devices
- ✅ Works with debug builds
- ✅ Works with sideloaded apps
- ✅ No installer restrictions

### Production Mode Requirements:
- ❌ Must be installed from Play Store
- ❌ Must be signed release build
- ❌ Must pass Google Play security checks

## 🎯 Summary

The fix ensures that:
1. **Development builds** automatically use SANDBOX environment
2. **Production builds** only use PRODUCTION when installed from Play Store
3. **Manual override** available via configuration flag
4. **No code changes needed** for different environments

This resolves the installer source error while maintaining proper environment separation for development and production.
